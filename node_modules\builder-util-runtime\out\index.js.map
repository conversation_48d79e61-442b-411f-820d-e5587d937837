{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AA8CA,0BAQC;AArDD,yDAA0E;AAAjE,sHAAA,iBAAiB,OAAA;AAAE,sHAAA,iBAAiB,OAAA;AAC7C,iCAAkC;AAAzB,iGAAA,QAAQ,OAAA;AACjB,+CAauB;AAZrB,uHAAA,uBAAuB,OAAA;AACvB,8HAAA,8BAA8B,OAAA;AAC9B,mHAAA,mBAAmB,OAAA;AACnB,+GAAA,eAAe,OAAA;AACf,+GAAA,eAAe,OAAA;AAEf,yGAAA,SAAS,OAAA;AACT,4GAAA,YAAY,OAAA;AACZ,yGAAA,SAAS,OAAA;AAET,6GAAA,aAAa,OAAA;AACb,iHAAA,iBAAiB,OAAA;AAEnB,uCAAqC;AAA5B,oGAAA,QAAQ,OAAA;AACjB,yEAAqF;AAA5E,sIAAA,yBAAyB,OAAA;AAClC,mDAeyB;AATvB,0HAAA,wBAAwB,OAAA;AAExB,2GAAA,SAAS,OAAA;AAQX,iCAA+B;AAAtB,8FAAA,KAAK,OAAA;AACd,iDAAyC;AAAhC,wGAAA,OAAO,OAAA;AAEhB,+BAA6B;AAApB,4FAAA,IAAI,OAAA;AACb,6BAA0C;AAAjC,+FAAA,QAAQ,OAAA;AAAE,+FAAA,QAAQ,OAAA;AAE3B,OAAO;AACM,QAAA,+BAA+B,GAAG,eAAe,CAAA;AAC9D,WAAW;AACE,QAAA,6BAA6B,GAAG,YAAY,CAAA;AAEzD,SAAgB,OAAO,CAAI,CAAyB;IAClD,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;QACd,OAAO,EAAE,CAAA;IACX,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5B,OAAO,CAAC,CAAA;IACV,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,CAAC,CAAC,CAAA;IACZ,CAAC;AACH,CAAC", "sourcesContent": ["export { BlockMap } from \"./blockMapApi\"\nexport { CancellationError, CancellationToken } from \"./CancellationToken\"\nexport { newError } from \"./error\"\nexport {\n  configureRequestOptions,\n  configureRequestOptionsFromUrl,\n  configureRequestUrl,\n  createHttpError,\n  DigestTransform,\n  DownloadOptions,\n  HttpError,\n  HttpExecutor,\n  parseJson,\n  RequestHeaders,\n  safeGetHeader,\n  safeStringifyJson,\n} from \"./httpExecutor\"\nexport { MemoLazy } from \"./MemoLazy\"\nexport { ProgressCallbackTransform, ProgressInfo } from \"./ProgressCallbackTransform\"\nexport {\n  AllPublishOptions,\n  BaseS3Options,\n  BitbucketOptions,\n  CustomPublishOptions,\n  GenericServerOptions,\n  getS3LikeProviderBaseUrl,\n  GithubOptions,\n  githubUrl,\n  KeygenOptions,\n  PublishConfiguration,\n  PublishProvider,\n  S3Options,\n  SnapStoreOptions,\n  SpacesOptions,\n} from \"./publishOptions\"\nexport { retry } from \"./retry\"\nexport { parseDn } from \"./rfc2253Parser\"\nexport { BlockMapDataHolder, PackageFileInfo, ReleaseNoteInfo, UpdateFileInfo, UpdateInfo, WindowsUpdateInfo } from \"./updateInfo\"\nexport { UUID } from \"./uuid\"\nexport { parseXml, XElement } from \"./xml\"\n\n// nsis\nexport const CURRENT_APP_INSTALLER_FILE_NAME = \"installer.exe\"\n// nsis-web\nexport const CURRENT_APP_PACKAGE_FILE_NAME = \"package.7z\"\n\nexport function asArray<T>(v: Nullish | T | Array<T>): Array<T> {\n  if (v == null) {\n    return []\n  } else if (Array.isArray(v)) {\n    return v\n  } else {\n    return [v]\n  }\n}\n\nexport type Nullish = null | undefined\n"]}