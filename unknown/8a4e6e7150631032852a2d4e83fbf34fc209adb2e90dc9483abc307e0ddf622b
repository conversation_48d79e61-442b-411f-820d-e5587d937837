{"version": 3, "file": "publisher.js", "sourceRoot": "", "sources": ["../src/publisher.ts"], "names": [], "mappings": ";;;AA8CA,4BAWC;AAzDD,+CAA2C;AAC3C,+DAAiF;AACjF,+BAA8B;AAC9B,uCAAkD;AAIlD,MAAM,kBAAkB,GAAG;IACzB,UAAU,EAAE,GAAG;IACf,KAAK,EAAE,EAAE;CACV,CAAA;AAED,MAAsB,SAAS;IAC7B,YAAyC,OAAuB;QAAvB,YAAO,GAAP,OAAO,CAAgB;IAAG,CAAC;IAM1D,iBAAiB,CAAC,QAAgB,EAAE,IAAY;QACxD,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,WAAW,CAAC,CAAA;QACtE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;YACvD,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,sBAAO,GAAG,CAAC,CAAC,2BAA2B,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,YAAY,EAAE,EAAE;YAC3I,KAAK,EAAE,IAAI;YACX,GAAG,kBAAkB;SACtB,CAAC,CAAA;IACJ,CAAC;IAES,8BAA8B,CAAC,IAAY,EAAE,QAAe,EAAE,WAA+B,EAAE,MAA8B;QACrI,MAAM,eAAe,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,CAAA;QAC9C,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAEnC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,OAAO,eAAe,CAAA;QACxB,CAAC;aAAM,CAAC;YACN,MAAM,cAAc,GAAG,IAAI,gDAAyB,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAA;YACrI,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YAClC,OAAO,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC7C,CAAC;IACH,CAAC;CAGF;AAhCD,8BAgCC;AAED,SAAgB,QAAQ;IACtB,MAAM,GAAG,GACP,OAAO,CAAC,GAAG,CAAC,UAAU;QACtB,OAAO,CAAC,GAAG,CAAC,sBAAsB;QAClC,OAAO,CAAC,GAAG,CAAC,UAAU;QACtB,OAAO,CAAC,GAAG,CAAC,eAAe;QAC3B,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,kDAAkD;QAC9E,OAAO,CAAC,GAAG,CAAC,aAAa;QACzB,OAAO,CAAC,GAAG,CAAC,aAAa;QACzB,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IAC9E,OAAO,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAA;AACnD,CAAC", "sourcesContent": ["import { log, PADDING } from \"builder-util\"\nimport { ProgressCallbackTransform, PublishProvider } from \"builder-util-runtime\"\nimport * as chalk from \"chalk\"\nimport { createReadStream, Stats } from \"fs-extra\"\nimport { PublishContext, UploadTask } from \".\"\nimport { ProgressBar } from \"./progress\"\n\nconst progressBarOptions = {\n  incomplete: \" \",\n  width: 20,\n}\n\nexport abstract class Publisher {\n  protected constructor(protected readonly context: PublishContext) {}\n\n  abstract get providerName(): PublishProvider\n\n  abstract upload(task: UploadTask): Promise<any>\n\n  protected createProgressBar(fileName: string, size: number): ProgressBar | null {\n    log.info({ file: fileName, provider: this.providerName }, \"uploading\")\n    if (this.context.progress == null || size < 512 * 1024) {\n      return null\n    }\n    return this.context.progress.createBar(`${\" \".repeat(PADDING + 2)}[:bar] :percent :etas | ${chalk.green(fileName)} to ${this.providerName}`, {\n      total: size,\n      ...progressBarOptions,\n    })\n  }\n\n  protected createReadStreamAndProgressBar(file: string, fileStat: Stats, progressBar: ProgressBar | null, reject: (error: Error) => void): NodeJS.ReadableStream {\n    const fileInputStream = createReadStream(file)\n    fileInputStream.on(\"error\", reject)\n\n    if (progressBar == null) {\n      return fileInputStream\n    } else {\n      const progressStream = new ProgressCallbackTransform(fileStat.size, this.context.cancellationToken, it => progressBar.tick(it.delta))\n      progressStream.on(\"error\", reject)\n      return fileInputStream.pipe(progressStream)\n    }\n  }\n\n  abstract toString(): string\n}\n\nexport function getCiTag() {\n  const tag =\n    process.env.TRAVIS_TAG ||\n    process.env.APPVEYOR_REPO_TAG_NAME ||\n    process.env.CIRCLE_TAG ||\n    process.env.BITRISE_GIT_TAG ||\n    process.env.CI_BUILD_TAG || // deprecated, GitLab uses `CI_COMMIT_TAG` instead\n    process.env.CI_COMMIT_TAG ||\n    process.env.BITBUCKET_TAG ||\n    (process.env.GITHUB_REF_TYPE === \"tag\" ? process.env.GITHUB_REF_NAME : null)\n  return tag != null && tag.length > 0 ? tag : null\n}\n"]}