{"version": 3, "file": "baseS3Publisher.js", "sourceRoot": "", "sources": ["../../src/s3/baseS3Publisher.ts"], "names": [], "mappings": ";;;AAAA,+CAAqD;AAErD,0CAA4C;AAC5C,6BAA4B;AAE5B,4CAAwC;AAExC,MAAsB,eAAgB,SAAQ,qBAAS;IACrD,YACE,OAAuB,EACf,OAAsB;QAE9B,KAAK,CAAC,OAAO,CAAC,CAAA;QAFN,YAAO,GAAP,OAAO,CAAe;IAGhC,CAAC;IAIS,kBAAkB,CAAC,IAAmB;QAC9C,wCAAwC;QACxC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,aAAa,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAED,oGAAoG;IACpG,KAAK,CAAC,MAAM,CAAC,IAAgB;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACzC,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAA;QAExD,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAA;QAEpF,MAAM,IAAI,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACnG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;QAE7B,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,IAAI,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAA;YACrE,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;YACxD,MAAM,IAAA,kBAAO,EAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAClC,OAAM;QACR,CAAC;QAED,+CAA+C;QAC/C,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAA;QACpC,6BAA6B;QAC7B,uDAAuD;QACvD,oCAAoC;QACpC,0CAA0C;QAC1C,iEAAiE;QACjE,QAAQ;QACR,OAAO;QACP,IAAI;QAEJ,OAAO,MAAM,iBAAiB,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE;YACzE,IAAA,gCAAiB,EAAC,IAAI,EAAE,OAAO,CAAC,EAAE;gBAChC,QAAQ,CAAC,GAAG,EAAE;oBACZ,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBACxB,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC;iBACC,IAAI,CAAC,GAAG,EAAE;gBACT,IAAI,CAAC;oBACH,kBAAG,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,EAAE,UAAU,CAAC,CAAA;gBACtG,CAAC;wBAAS,CAAC;oBACT,OAAO,CAAC,SAAS,CAAC,CAAA;gBACpB,CAAC;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,MAAM,CAAC,CAAA;QAClB,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,QAAQ;QACN,OAAO,GAAG,IAAI,CAAC,YAAY,aAAa,IAAI,CAAC,aAAa,EAAE,GAAG,CAAA;IACjE,CAAC;CACF;AAjED,0CAiEC", "sourcesContent": ["import { executeAppBuilder, log } from \"builder-util\"\nimport { BaseS3Options } from \"builder-util-runtime\"\nimport { mkdir, symlink } from \"fs/promises\"\nimport * as path from \"path\"\nimport { PublishContext, UploadTask } from \"..\"\nimport { Publisher } from \"../publisher\"\n\nexport abstract class BaseS3Publisher extends Publisher {\n  protected constructor(\n    context: PublishContext,\n    private options: BaseS3Options\n  ) {\n    super(context)\n  }\n\n  protected abstract getBucketName(): string\n\n  protected configureS3Options(args: Array<string>) {\n    // if explicitly set to null, do not add\n    if (this.options.acl !== null) {\n      args.push(\"--acl\", this.options.acl || \"public-read\")\n    }\n  }\n\n  // http://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/s3-example-creating-buckets.html\n  async upload(task: UploadTask): Promise<any> {\n    const fileName = path.basename(task.file)\n    const cancellationToken = this.context.cancellationToken\n\n    const target = (this.options.path == null ? \"\" : `${this.options.path}/`) + fileName\n\n    const args = [\"publish-s3\", \"--bucket\", this.getBucketName(), \"--key\", target, \"--file\", task.file]\n    this.configureS3Options(args)\n\n    if (process.env.__TEST_S3_PUBLISHER__ != null) {\n      const testFile = path.join(process.env.__TEST_S3_PUBLISHER__, target)\n      await mkdir(path.dirname(testFile), { recursive: true })\n      await symlink(task.file, testFile)\n      return\n    }\n\n    // https://github.com/aws/aws-sdk-go/issues/279\n    this.createProgressBar(fileName, -1)\n    // if (progressBar != null) {\n    //   const callback = new ProgressCallback(progressBar)\n    //   uploader.on(\"progress\", () => {\n    //     if (!cancellationToken.cancelled) {\n    //       callback.update(uploader.loaded, uploader.contentLength)\n    //     }\n    //   })\n    // }\n\n    return await cancellationToken.createPromise((resolve, reject, onCancel) => {\n      executeAppBuilder(args, process => {\n        onCancel(() => {\n          process.kill(\"SIGINT\")\n        })\n      })\n        .then(() => {\n          try {\n            log.debug({ provider: this.providerName, file: fileName, bucket: this.getBucketName() }, \"uploaded\")\n          } finally {\n            resolve(undefined)\n          }\n        })\n        .catch(reject)\n    })\n  }\n\n  toString() {\n    return `${this.providerName} (bucket: ${this.getBucketName()})`\n  }\n}\n"]}