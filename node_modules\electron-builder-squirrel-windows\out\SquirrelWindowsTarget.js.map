{"version": 3, "file": "SquirrelWindowsTarget.js", "sourceRoot": "", "sources": ["../src/SquirrelWindowsTarget.ts"], "names": [], "mappings": ";;AAAA,+CAA8E;AAC9E,wDAA4D;AAC5D,qDAAkG;AAClG,6BAA4B;AAC5B,yBAAwB;AACxB,yBAAwB;AACxB,6DAAwG;AAExG,MAAqB,qBAAsB,SAAQ,wBAAM;IAIvD,YACmB,QAAqB,EAC7B,MAAc;QAEvB,KAAK,CAAC,UAAU,CAAC,CAAA;QAHA,aAAQ,GAAR,QAAQ,CAAa;QAC7B,WAAM,GAAN,MAAM,CAAQ;QALzB,2DAA2D;QAClD,YAAO,GAA2B,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,EAA4B,CAAA;IAO/J,CAAC;IAEO,KAAK,CAAC,4BAA4B;QACxC,yJAAyJ;QACzJ,mKAAmK;QACnK,IAAI,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kCAAkC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;QAC5I,IAAI,IAAA,8BAAe,EAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;YACxE,kBAAG,CAAC,IAAI,CAAC,EAAE,eAAe,EAAE,EAAE,iGAAiG,CAAC,CAAA;YAChI,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kCAAkC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;QAClG,CAAC;QAED,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,yBAAyB,EAAE,CAAC,CAAA;QACvH,iDAAiD;QACjD,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAC9E,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,EAAE,EAAE,kBAAkB,EAAE,EAAE,yBAAyB,CAAC,CAAA;QAEvF,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAA;QAC3D,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;gBACpD,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,2BAA2B,CAAC,CAAA;gBAC1D,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACpC,CAAC;QACH,CAAC;QAED,OAAO,kBAAkB,CAAA;IAC3B,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAA;QACxC,MAAM,aAAa,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAEpD,MAAM,SAAS,GAAG,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,wCAAwC,CAAC,CAAA;QACzH,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,mBAAmB,IAAA,+BAAa,EAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACxF,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,CAAA;QAC1D,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,wCAAwC,CAAC,CAAC,CAAA;QAE3J,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,kBAAkB;YACzC,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,eAAe,EAAE,SAAS,CAAC,CAAA;QACjG,MAAM,IAAA,4CAAsB,EAAC,WAAW,CAAC,CAAA;QAEzC,MAAM,QAAQ,CAAC,oBAAoB,CAAC,YAAY,EAAE,IAAI,EAAE,eAAe,CAAC,CAAA;QACxE,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACrB,MAAM,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QACtC,CAAC;QAED,MAAM,gBAAgB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,aAAa,UAAU,OAAO,GAAG,IAAA,+BAAa,EAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;QAE1G,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,gBAAgB,EAAE,gBAAgB,CAAC,KAAK,CAAC;YACzC,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAA;QAEF,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACrB,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;gBAC7C,IAAI,EAAE,eAAe;gBACrB,MAAM,EAAE,IAAI;gBACZ,IAAI;gBACJ,gBAAgB,EAAE,gBAAgB,CAAC,KAAK,CAAC;gBACzC,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,GAAG,IAAI,CAAC,OAAO,IAAI,IAAA,oCAAc,EAAC,OAAO,CAAC,GAAG,CAAA;QACnE,MAAM,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC;YACtC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,aAAa,YAAY,CAAC;YAC9D,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;SACT,CAAC,CAAA;QACF,IAAI,WAAW,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;YACvC,MAAM,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC;gBACtC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,aAAa,aAAa,CAAC;gBAC/D,MAAM,EAAE,IAAI;gBACZ,IAAI;gBACJ,QAAQ;aACT,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC;YACtC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC;YAC5C,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;SACT,CAAC,CAAA;IACJ,CAAC;IAED,IAAY,OAAO;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAA;IACxD,CAAC;IAEO,cAAc,CAAC,eAAuB;QAC5C,mFAAmF;QACnF,iEAAiE;QACjE,8DAA8D;QAC9D,MAAM,YAAY,GAAG,EAAE,CAAC,IAAI,CAAA;QAC5B,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,YAAY,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,CAAA;QAC3G,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,YAAY,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,CAAA;IAC7G,CAAC;IAEO,KAAK,CAAC,kCAAkC;QAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAAA;QAC7E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAA;QAClE,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC,CAAA;YAC7H,IAAI,eAAe,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;YACtE,MAAM,YAAY,GAAG,yCAAyC,CAAA;YAC9D,eAAe,GAAG,eAAe,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,YAAY,qBAAqB,UAAU,eAAe,CAAC,CAAA;YACtH,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,eAAe,CAAC,CAAA;YAC5D,OAAO,cAAc,CAAA;QACvB,CAAC;QACD,OAAO,YAAY,CAAA;IACrB,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,YAAoB,EAAE,eAAuB,EAAE,SAAiB;QAChG,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;QAClC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAA;YAC/C,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,OAAO,GAAG,sBAAsB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,gBAAgB,QAAQ,CAAC,IAAI,CAAC,6BAA6B,oBAAoB,CAAA;YAC1I,CAAC;YAED,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,MAAM,IAAI,wCAAyB,CAAC,iIAAiI,CAAC,CAAA;YACxK,CAAC;QACH,CAAC;QAED,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACrC,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;QAChC,MAAM,OAAO,GAAoB;YAC/B,YAAY,EAAE,YAAY;YAC1B,eAAe,EAAE,eAAe;YAChC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;YAC3D,KAAK,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI;YAC1C,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,GAAG,EAAE,GAAG,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,WAAW,MAAM;YACjF,OAAO,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;YAClC,cAAc,EAAE,MAAM,IAAI,CAAC,kCAAkC,EAAE;YAC/D,OAAO;YACP,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG;YACxB,cAAc,EAAE,KAAK;SACtB,CAAA;QAED,OAAO,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAA;QACnE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;QAC5C,OAAO,CAAC,UAAU,GAAG,IAAI,CAAA;QACzB,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAA;QAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACrB,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QACtD,CAAC;QAED,IAAI,IAAA,8BAAe,EAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YACzC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,WAAW,CAAA;QAChE,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA;QACxE,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAA;YAC/C,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,kBAAG,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAA;YACxE,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,cAAc,GAAG,sBAAsB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAA;gBAC1E,kBAAG,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,EAAE,uBAAuB,CAAC,CAAA;YAC/E,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,QAAQ,IAAI,CAAC,IAAA,8BAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YAC5G,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAA;QACtD,CAAC;QAED,IAAI,CAAC,CAAC,YAAY,IAAI,OAAO,CAAC,EAAE,CAAC;YAC/B,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAA;YAChD,IAAI,YAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;gBACjD,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,CAAA;YACnF,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;CACF;AAzMD,wCAyMC;AAED,SAAS,uBAAuB,CAAC,OAAY;IAC3C,KAAK,MAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE,cAAc,EAAE,KAAK,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,UAAU,CAAC,EAAE,CAAC;QACpK,IAAI,IAAI,IAAI,OAAO,EAAE,CAAC;YACpB,MAAM,IAAI,wCAAyB,CAAC,UAAU,IAAI,iCAAiC,CAAC,CAAA;QACtF,CAAC;IACH,CAAC;IAED,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;QACvB,kBAAG,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAA;QACrG,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAA;IAC9B,CAAC;IAED,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;IACvB,IAAI,GAAG,IAAI,IAAI,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE,CAAC;QAC5C,MAAM,IAAI,wCAAyB,CAAC,kDAAkD,GAAG,kBAAkB,CAAC,CAAA;IAC9G,CAAC;AACH,CAAC", "sourcesContent": ["import { InvalidConfigurationError, log, isEmptyOrSpaces } from \"builder-util\"\nimport { sanitizeFileName } from \"builder-util/out/filename\"\nimport { Arch, getArchSuffix, SquirrelWindowsOptions, Target, WinPackager } from \"app-builder-lib\"\nimport * as path from \"path\"\nimport * as fs from \"fs\"\nimport * as os from \"os\"\nimport { Options as SquirrelOptions, createWindowsInstaller, convertVersion } from \"electron-winstaller\"\n\nexport default class SquirrelWindowsTarget extends Target {\n  //tslint:disable-next-line:no-object-literal-type-assertion\n  readonly options: SquirrelWindowsOptions = { ...this.packager.platformSpecificBuildOptions, ...this.packager.config.squirrelWindows } as SquirrelWindowsOptions\n\n  constructor(\n    private readonly packager: WinPackager,\n    readonly outDir: string\n  ) {\n    super(\"squirrel\")\n  }\n\n  private async prepareSignedVendorDirectory(): Promise<string> {\n    // If not specified will use the Squirrel.Windows that is shipped with electron-installer(https://github.com/electron/windows-installer/tree/main/vendor)\n    // After https://github.com/electron-userland/electron-builder-binaries/pull/56 merged, will add `electron-builder-binaries` to get the latest version of squirrel.\n    let vendorDirectory = this.options.customSquirrelVendorDir || path.join(require.resolve(\"electron-winstaller/package.json\"), \"..\", \"vendor\")\n    if (isEmptyOrSpaces(vendorDirectory) || !fs.existsSync(vendorDirectory)) {\n      log.warn({ vendorDirectory }, \"unable to access Squirrel.Windows vendor directory, falling back to default electron-winstaller\")\n      vendorDirectory = path.join(require.resolve(\"electron-winstaller/package.json\"), \"..\", \"vendor\")\n    }\n\n    const tmpVendorDirectory = await this.packager.info.tempDirManager.createTempDir({ prefix: \"squirrel-windows-vendor\" })\n    // Copy entire vendor directory to temp directory\n    await fs.promises.cp(vendorDirectory, tmpVendorDirectory, { recursive: true })\n    log.debug({ from: vendorDirectory, to: tmpVendorDirectory }, \"copied vendor directory\")\n\n    const files = await fs.promises.readdir(tmpVendorDirectory)\n    for (const file of files) {\n      if ([\"Squirrel.exe\", \"StubExecutable.exe\"].includes(file)) {\n        const filePath = path.join(tmpVendorDirectory, file)\n        log.debug({ file: filePath }, \"signing vendor executable\")\n        await this.packager.sign(filePath)\n      }\n    }\n\n    return tmpVendorDirectory\n  }\n\n  async build(appOutDir: string, arch: Arch) {\n    const packager = this.packager\n    const version = packager.appInfo.version\n    const sanitizedName = sanitizeFileName(this.appName)\n\n    const setupFile = packager.expandArtifactNamePattern(this.options, \"exe\", arch, \"${productName} Setup ${version}.${ext}\")\n    const installerOutDir = path.join(this.outDir, `squirrel-windows${getArchSuffix(arch)}`)\n    const artifactPath = path.join(installerOutDir, setupFile)\n    const msiArtifactPath = path.join(installerOutDir, packager.expandArtifactNamePattern(this.options, \"msi\", arch, \"${productName} Setup ${version}.${ext}\"))\n\n    await packager.info.emitArtifactBuildStarted({\n      targetPresentableName: \"Squirrel.Windows\",\n      file: artifactPath,\n      arch,\n    })\n\n    const distOptions = await this.computeEffectiveDistOptions(appOutDir, installerOutDir, setupFile)\n    await createWindowsInstaller(distOptions)\n\n    await packager.signAndEditResources(artifactPath, arch, installerOutDir)\n    if (this.options.msi) {\n      await packager.sign(msiArtifactPath)\n    }\n\n    const safeArtifactName = (ext: string) => `${sanitizedName}-Setup-${version}${getArchSuffix(arch)}.${ext}`\n\n    await packager.info.emitArtifactBuildCompleted({\n      file: artifactPath,\n      target: this,\n      arch,\n      safeArtifactName: safeArtifactName(\"exe\"),\n      packager: this.packager,\n    })\n\n    if (this.options.msi) {\n      await packager.info.emitArtifactBuildCompleted({\n        file: msiArtifactPath,\n        target: this,\n        arch,\n        safeArtifactName: safeArtifactName(\"msi\"),\n        packager: this.packager,\n      })\n    }\n\n    const packagePrefix = `${this.appName}-${convertVersion(version)}-`\n    await packager.info.emitArtifactCreated({\n      file: path.join(installerOutDir, `${packagePrefix}full.nupkg`),\n      target: this,\n      arch,\n      packager,\n    })\n    if (distOptions.remoteReleases != null) {\n      await packager.info.emitArtifactCreated({\n        file: path.join(installerOutDir, `${packagePrefix}delta.nupkg`),\n        target: this,\n        arch,\n        packager,\n      })\n    }\n\n    await packager.info.emitArtifactCreated({\n      file: path.join(installerOutDir, \"RELEASES\"),\n      target: this,\n      arch,\n      packager,\n    })\n  }\n\n  private get appName() {\n    return this.options.name || this.packager.appInfo.name\n  }\n\n  private select7zipArch(vendorDirectory: string) {\n    // https://github.com/electron/windows-installer/blob/main/script/select-7z-arch.js\n    // Even if we're cross-compiling for a different arch like arm64,\n    // we still need to use the 7-Zip executable for the host arch\n    const resolvedArch = os.arch\n    fs.copyFileSync(path.join(vendorDirectory, `7z-${resolvedArch}.exe`), path.join(vendorDirectory, \"7z.exe\"))\n    fs.copyFileSync(path.join(vendorDirectory, `7z-${resolvedArch}.dll`), path.join(vendorDirectory, \"7z.dll\"))\n  }\n\n  private async createNuspecTemplateWithProjectUrl() {\n    const templatePath = path.resolve(__dirname, \"..\", \"template.nuspectemplate\")\n    const projectUrl = await this.packager.appInfo.computePackageUrl()\n    if (projectUrl != null) {\n      const nuspecTemplate = await this.packager.info.tempDirManager.getTempFile({ prefix: \"template\", suffix: \".nuspectemplate\" })\n      let templateContent = await fs.promises.readFile(templatePath, \"utf8\")\n      const searchString = \"<copyright><%- copyright %></copyright>\"\n      templateContent = templateContent.replace(searchString, `${searchString}\\n    <projectUrl>${projectUrl}</projectUrl>`)\n      await fs.promises.writeFile(nuspecTemplate, templateContent)\n      return nuspecTemplate\n    }\n    return templatePath\n  }\n\n  async computeEffectiveDistOptions(appDirectory: string, outputDirectory: string, setupFile: string): Promise<SquirrelOptions> {\n    const packager = this.packager\n    let iconUrl = this.options.iconUrl\n    if (iconUrl == null) {\n      const info = await packager.info.repositoryInfo\n      if (info != null) {\n        iconUrl = `https://github.com/${info.user}/${info.project}/blob/master/${packager.info.relativeBuildResourcesDirname}/icon.ico?raw=true`\n      }\n\n      if (iconUrl == null) {\n        throw new InvalidConfigurationError(\"squirrelWindows.iconUrl is not specified, please see https://www.electron.build/squirrel-windows#SquirrelWindowsOptions-iconUrl\")\n      }\n    }\n\n    checkConflictingOptions(this.options)\n    const appInfo = packager.appInfo\n    const options: SquirrelOptions = {\n      appDirectory: appDirectory,\n      outputDirectory: outputDirectory,\n      name: this.options.useAppIdAsId ? appInfo.id : this.appName,\n      title: appInfo.productName || appInfo.name,\n      version: appInfo.version,\n      description: appInfo.description,\n      exe: `${appInfo.productFilename || this.options.name || appInfo.productName}.exe`,\n      authors: appInfo.companyName || \"\",\n      nuspecTemplate: await this.createNuspecTemplateWithProjectUrl(),\n      iconUrl,\n      copyright: appInfo.copyright,\n      noMsi: !this.options.msi,\n      usePackageJson: false,\n    }\n\n    options.vendorDirectory = await this.prepareSignedVendorDirectory()\n    this.select7zipArch(options.vendorDirectory)\n    options.fixUpPaths = true\n    options.setupExe = setupFile\n    if (this.options.msi) {\n      options.setupMsi = setupFile.replace(\".exe\", \".msi\")\n    }\n\n    if (isEmptyOrSpaces(options.description)) {\n      options.description = this.options.name || appInfo.productName\n    }\n\n    if (options.remoteToken == null) {\n      options.remoteToken = process.env.GH_TOKEN || process.env.GITHUB_TOKEN\n    }\n\n    if (this.options.remoteReleases === true) {\n      const info = await packager.info.repositoryInfo\n      if (info == null) {\n        log.warn(\"remoteReleases set to true, but cannot get repository info\")\n      } else {\n        options.remoteReleases = `https://github.com/${info.user}/${info.project}`\n        log.info({ remoteReleases: options.remoteReleases }, `remoteReleases is set`)\n      }\n    } else if (typeof this.options.remoteReleases === \"string\" && !isEmptyOrSpaces(this.options.remoteReleases)) {\n      options.remoteReleases = this.options.remoteReleases\n    }\n\n    if (!(\"loadingGif\" in options)) {\n      const resourceList = await packager.resourceList\n      if (resourceList.includes(\"install-spinner.gif\")) {\n        options.loadingGif = path.join(packager.buildResourcesDir, \"install-spinner.gif\")\n      }\n    }\n\n    return options\n  }\n}\n\nfunction checkConflictingOptions(options: any) {\n  for (const name of [\"outputDirectory\", \"appDirectory\", \"exe\", \"fixUpPaths\", \"usePackageJson\", \"extraFileSpecs\", \"extraMetadataSpecs\", \"skipUpdateIcon\", \"setupExe\"]) {\n    if (name in options) {\n      throw new InvalidConfigurationError(`Option ${name} is ignored, do not specify it.`)\n    }\n  }\n\n  if (\"noMsi\" in options) {\n    log.warn(`noMsi is deprecated, please specify as \"msi\": true if you want to create an MSI installer`)\n    options.msi = !options.noMsi\n  }\n\n  const msi = options.msi\n  if (msi != null && typeof msi !== \"boolean\") {\n    throw new InvalidConfigurationError(`msi expected to be boolean value, but string '\"${msi}\"' was specified`)\n  }\n}\n"]}