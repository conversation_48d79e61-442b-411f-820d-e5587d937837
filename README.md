# Hookah Ads - Advertisement Banner App

A desktop application for displaying advertisement banners at the bottom of screens in hookah lounges.

## Features

- **Overlay Banner**: Displays a transparent banner at the bottom of the screen
- **Text Ads**: Add scrolling text advertisements
- **Image Ads**: Include pictures in your advertisements
- **Customizable**: Adjust colors, height, and scroll speed
- **Control Panel**: Easy-to-use interface for managing ads

## Quick Start

### For End Users (Portable Version)

1. **Download**: Get the `HookahAds-Portable.exe` file
2. **Run**: Double-click the executable - no installation required!
3. **Use**: Two windows will open:
   - **Banner Window**: The advertisement display (appears at bottom of screen)
   - **Control Panel**: Where you manage your ads

### How to Use

1. **Add Text**: Type your advertisement text in the "Banner Text" field
2. **Adjust Height**: Use the slider to set banner height (2cm - 8cm)
3. **Choose Colors**: Pick background and text colors
4. **Set Speed**: Adjust how fast the text scrolls
5. **Add Images**: Click "Add Images" to include pictures
6. **Apply**: Click "Apply Changes" to update the banner

### Tips

- The banner will always stay on top of other windows
- Perfect for displaying over YouTube videos or other content
- Images will automatically resize to fit the banner height
- The app remembers your last settings

## For Developers

### Development Setup

```bash
# Install dependencies
npm install

# Run in development mode
npm start

# Build portable executable
npm run build-portable

# Build all platforms
npm run build-all
```

### Building for Distribution

```bash
# Create portable Windows executable
npm run dist
```

The built files will be in the `dist` folder.

### Project Structure

```
hookah-ads/
├── main.js          # Main Electron process
├── banner.html      # Banner display window
├── control.html     # Control panel window
├── package.json     # Project configuration
└── dist/           # Built executables (after build)
```

## System Requirements

- **Windows**: Windows 7 or later
- **Memory**: 100MB RAM
- **Disk Space**: 200MB free space
- **Display**: Any resolution (banner adapts to screen width)

## Troubleshooting

### Banner Not Showing
- Check if the banner window is minimized
- Ensure the control panel is open
- Try clicking "Apply Changes" again

### Images Not Loading
- Make sure image files exist in the selected location
- Supported formats: JPG, PNG, GIF, BMP
- Try using smaller image files (under 5MB each)

### Performance Issues
- Close unnecessary applications
- Use fewer/smaller images
- Increase scroll speed to reduce animation load

## Support

For technical support or feature requests, please contact the development team.

---

**Version**: 1.0.0  
**License**: MIT  
**Platform**: Windows, Linux, macOS
