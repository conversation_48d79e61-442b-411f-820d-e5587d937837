{"version": 3, "file": "yarnNodeModulesCollector.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/yarnNodeModulesCollector.ts"], "names": [], "mappings": ";;;AAAA,uEAAmE;AAEnE,MAAa,wBAAyB,SAAQ,iDAAuB;IACnE,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAA;QAGA,mBAAc,GAAG,OAAO,CAAC,OAAO,CAAC;YAC/C,GAAG,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM;YACvD,IAAI,EAAE,CAAC,SAAS,EAAE,mBAAmB,CAAC;YACtC,QAAQ,EAAE,WAAW;SACtB,CAAC,CAAA;IANF,CAAC;CAOF;AAVD,4DAUC", "sourcesContent": ["import { NpmNodeModulesCollector } from \"./npmNodeModulesCollector\"\n\nexport class YarnNodeModulesCollector extends NpmNodeModulesCollector {\n  constructor(rootDir: string) {\n    super(rootDir)\n  }\n\n  public readonly installOptions = Promise.resolve({\n    cmd: process.platform === \"win32\" ? \"yarn.cmd\" : \"yarn\",\n    args: [\"install\", \"--frozen-lockfile\"],\n    lockfile: \"yarn.lock\",\n  })\n}\n"]}