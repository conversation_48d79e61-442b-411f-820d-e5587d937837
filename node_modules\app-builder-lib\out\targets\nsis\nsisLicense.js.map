{"version": 3, "file": "nsisLicense.js", "sourceRoot": "", "sources": ["../../../src/targets/nsis/nsisLicense.ts"], "names": [], "mappings": ";;AAiCA,gDAkDC;AAnFD,+CAAkC;AAClC,yBAAwB;AACxB,6BAA4B;AAC5B,4CAAuC;AACvC,gDAAgF;AAIhF,yCAA6C;AAE7C,SAAS,4BAA4B,CAAC,QAAgB;;IACpD,IAAI,CAAC;QACH,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QACvD,MAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;QAEtC,oDAAoD;QACpD,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,8BAA8B,CAAC,CAAA;QAC3E,IAAI,IAAI,CAAC,MAAM,IAAI,eAAe,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;YAC9G,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,qDAAqD,CAAC,CAAA;YAClG,OAAO,IAAI,CAAA;QACb,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAAA;QAC1D,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;QACvC,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,+CAA+C,CAAC,CAAA;QAC5F,OAAO,IAAI,CAAA;IACb,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,MAAA,GAAG,CAAC,OAAO,mCAAI,GAAG,CAAC,KAAK,EAAE,EAAE,0CAA0C,CAAC,CAAA;QAC1H,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,kBAAkB,CAAC,QAAqB,EAAE,OAAoB,EAAE,eAAoC,EAAE,SAAwB;IAClJ,MAAM,OAAO,GAAG,MAAM,IAAA,oCAA0B,EAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;IAC3E,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,IAAI,WAA0B,CAAA;QAC9B,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,WAAW,GAAG;gBACZ,kDAAkD;gBAClD,sBAAsB;gBACtB,0CAA0C;gBAC1C,2BAA2B;gBAC3B,+DAA+D;gBAC/D,aAAa;gBAEb,kCAAkC,IAAI,CAAC,IAAI,CAAC,2BAAgB,EAAE,mBAAmB,CAAC,GAAG;aACtF,CAAA;QACH,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,CAAC,kCAAkC,OAAO,GAAG,CAAC,CAAA;QAC9D,CAAC;QAED,eAAe,CAAC,KAAK,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,eAAe,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,0CAA0C,OAAO,GAAG,CAAC,CAAC,CAAA;QAClG,CAAC;QACD,OAAM;IACR,CAAC;IAED,MAAM,YAAY,GAAG,MAAM,IAAA,yBAAe,EAAC,QAAQ,CAAC,CAAA;IACpD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAM;IACR,CAAC;IAED,MAAM,WAAW,GAAkB,EAAE,CAAA;IACrC,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAA;IAE3C,IAAI,WAAW,GAAkB,IAAI,CAAA;IACrC,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;QAChC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC5C,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACvC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,WAAW,GAAG,IAAI,CAAC,IAAI,CAAA;QACzB,CAAC;QACD,WAAW,CAAC,IAAI,CAAC,gCAAgC,YAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;IAC3G,CAAC;IAED,KAAK,MAAM,CAAC,IAAI,gBAAgB,EAAE,CAAC;QACjC,WAAW,CAAC,IAAI,CAAC,gCAAgC,YAAI,CAAC,CAAC,CAAC,KAAK,WAAW,GAAG,CAAC,CAAA;IAC9E,CAAC;IAED,WAAW,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAA;IACjE,eAAe,CAAC,KAAK,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;AACnD,CAAC", "sourcesContent": ["import { log } from \"builder-util\"\nimport * as fs from \"fs\"\nimport * as path from \"path\"\nimport { lcid } from \"../../util/langs\"\nimport { getLicenseFiles, getNotLocalizedLicenseFile } from \"../../util/license\"\nimport { WinPackager } from \"../../winPackager\"\nimport { NsisOptions } from \"./nsisOptions\"\nimport { NsisScriptGenerator } from \"./nsisScriptGenerator\"\nimport { nsisTemplatesDir } from \"./nsisUtil\"\n\nfunction convertFileToUtf8WithBOMSync(filePath: string): boolean {\n  try {\n    const UTF8_BOM_HEADER = Buffer.from([0xef, 0xbb, 0xbf])\n    const data = fs.readFileSync(filePath)\n\n    // Check if the file already starts with a UTF-8 BOM\n    log.debug({ file: log.filePath(filePath) }, \"checking file for BOM header\")\n    if (data.length >= UTF8_BOM_HEADER.length && data.subarray(0, UTF8_BOM_HEADER.length).equals(UTF8_BOM_HEADER)) {\n      log.debug({ file: log.filePath(filePath) }, \"file is already in BOM format, skipping conversion.\")\n      return true\n    }\n\n    // If not, add the BOM\n    const dataWithBOM = Buffer.concat([UTF8_BOM_HEADER, data])\n    fs.writeFileSync(filePath, dataWithBOM)\n    log.debug({ file: log.filePath(filePath) }, \"file successfully converted to UTF-8 with BOM\")\n    return true\n  } catch (err: any) {\n    log.error({ file: log.filePath(filePath), message: err.message ?? err.stack }, \"unable to convert file to UTF-8 with BOM\")\n    return false\n  }\n}\n\nexport async function computeLicensePage(packager: WinPackager, options: NsisOptions, scriptGenerator: NsisScriptGenerator, languages: Array<string>): Promise<void> {\n  const license = await getNotLocalizedLicenseFile(options.license, packager)\n  if (license != null) {\n    let licensePage: Array<string>\n    if (license.endsWith(\".html\")) {\n      licensePage = [\n        \"!define MUI_PAGE_CUSTOMFUNCTION_SHOW LicenseShow\",\n        \"Function LicenseShow\",\n        \"  FindWindow $R0 `#32770` `` $HWNDPARENT\",\n        \"  GetDlgItem $R0 $R0 1000\",\n        \"EmbedHTML::Load /replace $R0 file://$PLUGINSDIR\\\\license.html\",\n        \"FunctionEnd\",\n\n        `!insertmacro MUI_PAGE_LICENSE \"${path.join(nsisTemplatesDir, \"empty-license.txt\")}\"`,\n      ]\n    } else {\n      licensePage = [`!insertmacro MUI_PAGE_LICENSE \"${license}\"`]\n    }\n\n    scriptGenerator.macro(\"licensePage\", licensePage)\n    if (license.endsWith(\".html\")) {\n      scriptGenerator.macro(\"addLicenseFiles\", [`File /oname=$PLUGINSDIR\\\\license.html \"${license}\"`])\n    }\n    return\n  }\n\n  const licenseFiles = await getLicenseFiles(packager)\n  if (licenseFiles.length === 0) {\n    return\n  }\n\n  const licensePage: Array<string> = []\n  const unspecifiedLangs = new Set(languages)\n\n  let defaultFile: string | null = null\n  for (const item of licenseFiles) {\n    unspecifiedLangs.delete(item.langWithRegion)\n    convertFileToUtf8WithBOMSync(item.file)\n    if (defaultFile == null) {\n      defaultFile = item.file\n    }\n    licensePage.push(`LicenseLangString MUILicense ${lcid[item.langWithRegion] || item.lang} \"${item.file}\"`)\n  }\n\n  for (const l of unspecifiedLangs) {\n    licensePage.push(`LicenseLangString MUILicense ${lcid[l]} \"${defaultFile}\"`)\n  }\n\n  licensePage.push('!insertmacro MUI_PAGE_LICENSE \"$(MUILicense)\"')\n  scriptGenerator.macro(\"licensePage\", licensePage)\n}\n"]}