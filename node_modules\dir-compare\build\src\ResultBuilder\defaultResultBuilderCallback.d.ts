import { DifferenceState, DiffSet, Entry, PermissionDeniedState, Reason, Statistics } from '..';
import { ExtOptions } from '../ExtOptions';
export declare function defaultResultBuilderCallback(entry1: Entry, entry2: Entry, state: DifferenceState, level: number, relativePath: string, options: ExtOptions, statistics: Statistics, diffSet: DiffSet, reason: Reason, permissionDeniedState: PermissionDeniedState): void;
//# sourceMappingURL=defaultResultBuilderCallback.d.ts.map