{"name": "pe-library", "version": "0.4.1", "engines": {"node": ">=12", "npm": ">=6"}, "engineStrict": true, "description": "Node.js library for Portable Executable format", "main": "./dist/index.js", "module": "./dist/_esm/index.js", "exports": {"node": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "import": "./dist/_esm/index.js", "require": "./dist/index.js"}, "types": "./dist/index.d.ts", "author": "jet", "license": "MIT", "homepage": "https://github.com/jet2jet/pe-library-js", "keywords": ["javascript", "library", "pe", "pe-executable", "portable-executable", "exe"], "repository": {"type": "git", "url": "https://github.com/jet2jet/pe-library-js.git"}, "funding": {"type": "github", "url": "https://github.com/sponsors/jet2jet"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc -p ./tsconfig.app.json", "build:esm": "tsc -p ./tsconfig.app.esm.json && node ./tools/copyEsmFile.js", "lint": "npm run lint:prettier && npm run lint:eslint", "lint:eslint": "eslint -c .eslintrc.yml --ext .js,.jsx,.ts,.tsx .", "lint:eslint:fix": "eslint -c .eslintrc.yml --fix --ext .js,.jsx,.ts,.tsx .", "lint:fix": "npm run lint:prettier:fix && npm run lint:eslint:fix", "lint:prettier": "prettier --config ./.prettierrc.yml --check \"**/*.{js,jsx,ts,tsx,yml,json,md}\"", "lint:prettier:fix": "prettier --config ./.prettierrc.yml --write \"**/*.{js,jsx,ts,tsx,yml,json,md}\"", "test": "jest --config ./jest.config.basic.js", "version": "node ./tools/updateVersion.js ./src/main/version.ts && git add -A ./src/main/version.ts"}, "dependencies": {}, "devDependencies": {"@types/jest": "^28.1.8", "@types/node": "^12.20.37", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0", "eslint": "^8.31.0", "eslint-config-prettier": "^8.6.0", "eslint-config-standard": "^17.0.0", "eslint-config-standard-with-typescript": "^26.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.6.0", "eslint-plugin-promise": "^6.1.1", "jest": "^28.1.3", "prettier": "^2.8.2", "ts-jest": "^28.0.8", "typescript": "~4.2.4"}}