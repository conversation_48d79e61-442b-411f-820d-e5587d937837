{"version": 3, "file": "unpackDetector.js", "sourceRoot": "", "sources": ["../../src/asar/unpackDetector.ts"], "names": [], "mappings": ";;AAKA,gCAGC;AAGD,gDAmCC;AA9CD,+CAA+C;AAC/C,+CAA+C;AAC/C,6BAA4B;AAG5B,SAAgB,UAAU,CAAC,IAAY;IACrC,oEAAoE;IACpE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;AACpI,CAAC;AAED,gBAAgB;AAChB,SAAgB,kBAAkB,CAAC,OAAwB,EAAE,cAA2B;IACtF,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;IAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACrD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAC7B,MAAM,IAAI,GAAgB,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAA;QAC7C,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YACpE,SAAQ;QACV,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,SAAQ;QACV,CAAC;QAED,oEAAoE;QACpE,IAAI,YAAY,GAAG,KAAK,CAAA;QACxB,uEAAuE;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;QAC/C,IAAI,UAAU,KAAK,gBAAgB,IAAI,UAAU,KAAK,eAAe,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1F,YAAY,GAAG,IAAI,CAAA;QACrB,CAAC;aAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YACzB,YAAY,GAAG,CAAC,CAAC,IAAA,+BAAgB,EAAC,IAAI,CAAC,CAAA;QACzC,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,SAAQ;QACV,CAAC;QAED,IAAI,kBAAG,CAAC,cAAc,EAAE,CAAC;YACvB,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE,MAAM,EAAE,0BAA0B,EAAE,EAAE,8BAA8B,CAAC,CAAA;QAClH,CAAC;QACD,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IACzC,CAAC;AACH,CAAC", "sourcesContent": ["import { FilterStats, log } from \"builder-util\"\nimport { isBinaryFileSync } from \"isbinaryfile\"\nimport * as path from \"path\"\nimport { ResolvedFileSet } from \"../util/appFileCopier\"\n\nexport function isLibOrExe(file: string): boolean {\n  // https://github.com/electron-userland/electron-builder/issues/3038\n  return file.endsWith(\".dll\") || file.endsWith(\".exe\") || file.endsWith(\".dylib\") || file.endsWith(\".so\") || file.endsWith(\".node\")\n}\n\n/** @internal */\nexport function detectUnpackedDirs(fileSet: ResolvedFileSet, autoUnpackDirs: Set<string>) {\n  const metadata = fileSet.metadata\n\n  for (let i = 0, n = fileSet.files.length; i < n; i++) {\n    const file = fileSet.files[i]\n    const stat: FilterStats = metadata.get(file)!\n    if (!stat.moduleRootPath || autoUnpackDirs.has(stat.moduleRootPath)) {\n      continue\n    }\n\n    if (!stat.isFile()) {\n      continue\n    }\n\n    // https://github.com/electron-userland/electron-builder/issues/2679\n    let shouldUnpack = false\n    // ffprobe-static and ffmpeg-static are known packages to always unpack\n    const moduleName = stat.moduleName\n    const fileBaseName = path.basename(file)\n    const hasExtension = path.extname(fileBaseName)\n    if (moduleName === \"ffprobe-static\" || moduleName === \"ffmpeg-static\" || isLibOrExe(file)) {\n      shouldUnpack = true\n    } else if (!hasExtension) {\n      shouldUnpack = !!isBinaryFileSync(file)\n    }\n\n    if (!shouldUnpack) {\n      continue\n    }\n\n    if (log.isDebugEnabled) {\n      log.debug({ file: stat.moduleFullFilePath, reason: \"contains executable code\" }, \"not packed into asar archive\")\n    }\n    autoUnpackDirs.add(stat.moduleRootPath)\n  }\n}\n"]}