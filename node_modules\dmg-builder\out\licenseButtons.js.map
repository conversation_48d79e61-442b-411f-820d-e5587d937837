{"version": 3, "file": "licenseButtons.js", "sourceRoot": "", "sources": ["../src/licenseButtons.ts"], "names": [], "mappings": ";;AASA,sDASC;AASD,8CAiCC;AA3DD,8DAAmE;AACnE,+CAAkC;AAClC,uCAAmC;AACnC,oCAAmC;AACnC,qCAA8B;AAC9B,uCAA2C;AAC3C,mEAA2D;AAEpD,KAAK,UAAU,qBAAqB,CAAC,QAA+B;IACzE,OAAO,IAAA,0BAAgB,EACrB,CAAC,MAAM,QAAQ,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;QACxC,MAAM,IAAI,GAAG,EAAE,CAAC,WAAW,EAAE,CAAA;QAC7B,uCAAuC;QACvC,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;IAChG,CAAC,CAAC,EACF,QAAQ,CACT,CAAA;AACH,CAAC;AASM,KAAK,UAAU,iBAAiB,CAAC,kBAA6C,EAAE,cAAsB,EAAE,EAAU,EAAE,IAAY;IACrI,IAAI,IAAI,GAAG,IAAA,yCAAiB,EAAC,cAAc,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;IAEtD,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE,CAAC;QACtC,IAAI,IAAI,CAAC,cAAc,KAAK,cAAc,EAAE,CAAC;YAC3C,SAAQ;QACV,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAA,cAAI,EAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAQ,CAAA;YAChE,MAAM,UAAU,GACd,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC;gBACzD,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC;gBAC1D,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC;gBAC7D,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC;gBAC1D,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC;gBACzD,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;YAElE,IAAI,GAAG,gBAAgB,EAAE,MAAM,IAAI,QAAQ,CAAA;YAC3C,IAAI,IAAI,IAAA,yBAAe,EAAC,MAAM,GAAG,UAAU,CAAC,CAAA;YAC5C,IAAI,IAAI,MAAM,CAAA;YAEd,IAAI,kBAAG,CAAC,cAAc,EAAE,CAAC;gBACvB,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,6BAA6B,CAAC,CAAA;YACzE,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,kBAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,kCAAkC,CAAC,CAAA;YAC3D,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,UAAU,CAAC,KAAa,EAAE,IAAY,EAAE,cAAsB;IACrE,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAA;IAC3E,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACvC,OAAO,GAAG,GAAG,GAAG,CAAA;AAClB,CAAC;AAED,SAAS,WAAW,CAAC,EAAU;IAC7B,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAC1C,CAAC;AAED,SAAS,SAAS,CAAC,GAAW,EAAE,IAAY,EAAE,cAAsB;IAClE,MAAM,YAAY,GAAG,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;IACzD,IAAI,MAAM,GAAG,EAAE,CAAA;IAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,IAAI,CAAC;YACH,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC,CAAA;YAC7C,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;gBACtB,GAAG,GAAG,IAAI,CAAA,CAAC,GAAG;YAChB,CAAC;YACD,MAAM,IAAI,GAAG,CAAA;QACf,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,kBAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAA;YACvD,MAAM,IAAI,IAAI,CAAA,CAAC,GAAG;QACpB,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,cAAc,CAAC,IAAY,EAAE,cAAsB;IAC1D,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,IAAI,EAAE,UAAU;YACnB,OAAO,CAAC,QAAQ,CAAC,CAAA,CAAC,gBAAgB;QACpC,KAAK,IAAI,EAAE,SAAS;YAClB,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;gBAC/B,OAAO,CAAC,QAAQ,CAAC,CAAA,CAAC,oCAAoC;YACxD,CAAC;YACD,OAAO,CAAC,MAAM,CAAC,CAAA,CAAC,kCAAkC;QACpD,KAAK,IAAI,EAAE,QAAQ;YACjB,OAAO,CAAC,QAAQ,CAAC,CAAA,CAAC,cAAc;QAClC,KAAK,IAAI,CAAC,CAAC,QAAQ;QACnB,KAAK,IAAI,EAAE,MAAM;YACf,OAAO,CAAC,WAAW,CAAC,CAAA,CAAC,cAAc;QACrC,KAAK,IAAI,EAAE,QAAQ;YACjB,OAAO,CAAC,WAAW,CAAC,CAAA,CAAC,cAAc;QACrC,KAAK,IAAI,CAAC,CAAC,OAAO;QAClB,KAAK,KAAK,EAAE,OAAO;YACjB,OAAO,CAAC,UAAU,CAAC,CAAA,CAAC,aAAa;QACnC,KAAK,IAAI,CAAC,CAAC,SAAS;QACpB,KAAK,IAAI,CAAC,CAAC,aAAa;QACxB,KAAK,IAAI,CAAC,CAAC,SAAS;QACpB,KAAK,IAAI,CAAC,CAAC,WAAW;QACtB,KAAK,IAAI,EAAE,OAAO;YAChB,OAAO,CAAC,aAAa,CAAC,CAAA,CAAC,0BAA0B;QACnD,KAAK,IAAI,EAAE,UAAU;YACnB,OAAO,CAAC,YAAY,CAAC,CAAA,CAAC,gBAAgB;QACxC,KAAK,IAAI,EAAE,WAAW;YACpB,OAAO,CAAC,YAAY,CAAC,CAAA,CAAC,iBAAiB;QACzC,KAAK,IAAI,EAAE,MAAM;YACf,OAAO,CAAC,SAAS,CAAC,CAAA,CAAC,YAAY;QACjC,KAAK,IAAI,CAAC,CAAC,UAAU;QACrB,KAAK,IAAI,CAAC,CAAC,YAAY;QACvB,KAAK,IAAI,CAAC,CAAC,SAAS;QACpB,KAAK,IAAI,CAAC,CAAC,QAAQ;QACnB,KAAK,IAAI,CAAC,CAAC,WAAW;QACtB,KAAK,IAAI,CAAC,CAAC,OAAO;QAClB,KAAK,IAAI,EAAE,QAAQ;YACjB,OAAO,CAAC,aAAa,CAAC,CAAA,CAAC,gCAAgC;QACzD,KAAK,IAAI,CAAC,CAAC,WAAW;QACtB,KAAK,IAAI,EAAE,SAAS;YAClB,OAAO,CAAC,YAAY,CAAC,CAAA,CAAC,iBAAiB;QACzC,KAAK,IAAI,EAAE,SAAS;YAClB,OAAO,CAAC,YAAY,CAAC,CAAA,CAAC,eAAe;QACvC,KAAK,IAAI,CAAC,CAAC,UAAU;QACrB,KAAK,IAAI,EAAE,WAAW;YACpB,OAAO,CAAC,aAAa,CAAC,CAAA,CAAC,gBAAgB;QACzC;YACE,OAAO,CAAC,UAAU,CAAC,CAAA,CAAC,uBAAuB;IAC/C,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CAAC,GAAW,EAAE,CAAS,EAAE,YAAiB;IAC9D,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;IAC9B,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAC1B,CAAC;SAAM,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IACzD,CAAC;SAAM,CAAC;QACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YACpE,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACzB,OAAO,MAAM,CAAA;YACf,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC", "sourcesContent": ["import { PlatformPackager } from \"app-builder-lib\"\nimport { getLicenseAssets } from \"app-builder-lib/out/util/license\"\nimport { log } from \"builder-util\"\nimport { readFile } from \"fs-extra\"\nimport * as iconv from \"iconv-lite\"\nimport { load } from \"js-yaml\"\nimport { serializeString } from \"./dmgUtil\"\nimport { getDefaultButtons } from \"./licenseDefaultButtons\"\n\nexport async function getLicenseButtonsFile(packager: PlatformPackager<any>): Promise<Array<LicenseButtonsFile>> {\n  return getLicenseAssets(\n    (await packager.resourceList).filter(it => {\n      const name = it.toLowerCase()\n      // noinspection SpellCheckingInspection\n      return name.startsWith(\"licensebuttons_\") && (name.endsWith(\".json\") || name.endsWith(\".yml\"))\n    }),\n    packager\n  )\n}\n\nexport interface LicenseButtonsFile {\n  file: string\n  lang: string\n  langWithRegion: string\n  langName: string\n}\n\nexport async function getLicenseButtons(licenseButtonFiles: Array<LicenseButtonsFile>, langWithRegion: string, id: number, name: string) {\n  let data = getDefaultButtons(langWithRegion, id, name)\n\n  for (const item of licenseButtonFiles) {\n    if (item.langWithRegion !== langWithRegion) {\n      continue\n    }\n\n    try {\n      const fileData = load(await readFile(item.file, \"utf-8\")) as any\n      const buttonsStr =\n        labelToHex(fileData.lang, item.lang, item.langWithRegion) +\n        labelToHex(fileData.agree, item.lang, item.langWithRegion) +\n        labelToHex(fileData.disagree, item.lang, item.langWithRegion) +\n        labelToHex(fileData.print, item.lang, item.langWithRegion) +\n        labelToHex(fileData.save, item.lang, item.langWithRegion) +\n        labelToHex(fileData.description, item.lang, item.langWithRegion)\n\n      data = `data 'STR#' (${id}, \"${name}\") {\\n`\n      data += serializeString(\"0006\" + buttonsStr)\n      data += `\\n};`\n\n      if (log.isDebugEnabled) {\n        log.debug({ lang: item.langName, data }, `overwriting license buttons`)\n      }\n      return data\n    } catch (e: any) {\n      log.debug({ error: e }, \"cannot overwrite license buttons\")\n      return data\n    }\n  }\n\n  return data\n}\n\nfunction labelToHex(label: string, lang: string, langWithRegion: string) {\n  const lbl = hexEncode(label, lang, langWithRegion).toString().toUpperCase()\n  const len = numberToHex(lbl.length / 2)\n  return len + lbl\n}\n\nfunction numberToHex(nb: number) {\n  return (\"0\" + nb.toString(16)).slice(-2)\n}\n\nfunction hexEncode(str: string, lang: string, langWithRegion: string) {\n  const macCodePages = getMacCodePage(lang, langWithRegion)\n  let result = \"\"\n\n  for (let i = 0; i < str.length; i++) {\n    try {\n      let hex = getMacHexCode(str, i, macCodePages)\n      if (hex === undefined) {\n        hex = \"3F\" //?\n      }\n      result += hex\n    } catch (e: any) {\n      log.debug({ error: e, char: str[i] }, \"cannot convert\")\n      result += \"3F\" //?\n    }\n  }\n\n  return result\n}\n\nfunction getMacCodePage(lang: string, langWithRegion: string) {\n  switch (lang) {\n    case \"ja\": //japanese\n      return [\"euc-jp\"] //Apple Japanese\n    case \"zh\": //chinese\n      if (langWithRegion === \"zh_CN\") {\n        return [\"gb2312\"] //Apple Simplified Chinese (GB 2312)\n      }\n      return [\"big5\"] //Apple Traditional Chinese (Big5)\n    case \"ko\": //korean\n      return [\"euc-kr\"] //Apple Korean\n    case \"ar\": //arabic\n    case \"ur\": //urdu\n      return [\"macarabic\"] //Apple Arabic\n    case \"he\": //hebrew\n      return [\"machebrew\"] //Apple Hebrew\n    case \"el\": //greek\n    case \"elc\": //greek\n      return [\"macgreek\"] //Apple Greek\n    case \"ru\": //russian\n    case \"be\": //belarussian\n    case \"sr\": //serbian\n    case \"bg\": //bulgarian\n    case \"uz\": //uzbek\n      return [\"maccyrillic\"] //Apple Macintosh Cyrillic\n    case \"ro\": //romanian\n      return [\"macromania\"] //Apple Romanian\n    case \"uk\": //ukrainian\n      return [\"macukraine\"] //Apple Ukrainian\n    case \"th\": //thai\n      return [\"macthai\"] //Apple Thai\n    case \"et\": //estonian\n    case \"lt\": //lithuanian\n    case \"lv\": //latvian\n    case \"pl\": //polish\n    case \"hu\": //hungarian\n    case \"cs\": //czech\n    case \"sk\": //slovak\n      return [\"maccenteuro\"] //Apple Macintosh Central Europe\n    case \"is\": //icelandic\n    case \"fo\": //faroese\n      return [\"maciceland\"] //Apple Icelandic\n    case \"tr\": //turkish\n      return [\"macturkish\"] //Apple Turkish\n    case \"hr\": //croatian\n    case \"sl\": //slovenian\n      return [\"maccroatian\"] //Apple Croatian\n    default:\n      return [\"macroman\"] //Apple Macintosh Roman\n  }\n}\n\nfunction getMacHexCode(str: string, i: number, macCodePages: any) {\n  const code = str.charCodeAt(i)\n  if (code < 128) {\n    return code.toString(16)\n  } else if (code < 256) {\n    return iconv.encode(str[i], \"macroman\").toString(\"hex\")\n  } else {\n    for (let i = 0; i < macCodePages.length; i++) {\n      const result = iconv.encode(str[i], macCodePages[i]).toString(\"hex\")\n      if (result !== undefined) {\n        return result\n      }\n    }\n  }\n  return code\n}\n"]}