{"version": 3, "file": "signManager.js", "sourceRoot": "", "sources": ["../../src/codeSign/signManager.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Me<PERSON><PERSON><PERSON><PERSON>, Nullish } from \"builder-util-runtime\"\nimport { <PERSON><PERSON> } from \"lazy-val\"\nimport { Target } from \"../core\"\nimport { WindowsConfiguration } from \"../options/winOptions\"\nimport { WindowsSignOptions } from \"./windowsCodeSign\"\nimport { CertificateFromStoreInfo, FileCodeSigningInfo } from \"./windowsSignToolManager\"\n\nexport interface SignManager {\n  readonly computedPublisherName: Lazy<Array<string> | null>\n  readonly cscInfo: MemoLazy<WindowsConfiguration, FileCodeSigningInfo | CertificateFromStoreInfo | null>\n  computePublisherName(target: Target, publisherName: string | Nullish): Promise<string>\n  initialize(): Promise<void>\n  signFile(options: WindowsSignOptions): Promise<boolean>\n}\n"]}