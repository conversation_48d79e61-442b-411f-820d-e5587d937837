<!DOCTYPE html>
<html>
<head>
  <title>Banner Display</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background-color: transparent;
    }
    #banner {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
    }
    #content {
      white-space: nowrap;
      position: absolute;
      left: 100%;
      animation: scroll 20s linear infinite;
    }
    #content img {
      height: 80%;
      max-height: 150px;
      margin: 0 20px;
      vertical-align: middle;
      object-fit: contain;
    }
    @keyframes scroll {
      from { transform: translateX(0); }
      to { transform: translateX(calc(-100vw - 100%)); }
    }
  </style>
</head>
<body>
  <div id="banner">
    <div id="content"></div>
  </div>
  <script>
    const { ipcRenderer } = require('electron');

    ipcRenderer.on('apply-settings', (event, settings) => {
      try {
        const banner = document.getElementById('banner');
        const content = document.getElementById('content');

        // Apply banner height
        if (settings.height) {
          banner.style.height = settings.height + 'px';
        }

        // Apply colors
        if (settings.backgroundColor) {
          banner.style.backgroundColor = settings.backgroundColor;
        }
        if (settings.textColor) {
          content.style.color = settings.textColor;
        }

        // Apply font size (percentage of banner height)
        if (settings.fontSize && settings.height) {
          const fontSize = (settings.height * settings.fontSize / 100);
          content.style.fontSize = fontSize + 'px';
          content.style.lineHeight = settings.height + 'px';
        }

        // Create simple sequence: Text → Images → Text → Images...
        content.innerHTML = '';

        console.log('Creating banner content with settings:', settings);

        // Add text first
        if (settings.text) {
          const textSpan = document.createElement('span');
          textSpan.textContent = settings.text;
          textSpan.style.marginRight = '60px'; // Space after text
          textSpan.style.display = 'inline-block';
          content.appendChild(textSpan);
          console.log('Added text:', settings.text);
        }

        // Add visual elements after text (emoji, symbols, decorative text)
        if (settings.visuals && Array.isArray(settings.visuals) && settings.visuals.length > 0) {
          console.log('Processing visual elements:', settings.visuals);

          settings.visuals.forEach((visual, index) => {
            if (visual) {
              console.log(`Creating visual element ${index + 1}:`, visual);

              // Create visual element
              const visualElement = document.createElement('span');
              visualElement.textContent = visual;
              visualElement.style.display = 'inline-block';
              visualElement.style.margin = '0 30px';
              visualElement.style.fontSize = '1.2em'; // Slightly larger than text
              visualElement.style.verticalAlign = 'middle';

              // Add some special styling for visual impact
              visualElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
              visualElement.style.fontWeight = 'bold';

              content.appendChild(visualElement);
              console.log(`Added visual element ${index + 1} to content`);
            }
          });
        } else {
          console.log('No visual elements to process');
        }

        // Add picture elements after visual elements (built-in icons and text)
        if (settings.pictures && Array.isArray(settings.pictures) && settings.pictures.length > 0) {
          console.log('Processing picture elements:', settings.pictures);

          settings.pictures.forEach((picture, index) => {
            if (picture) {
              console.log(`Creating picture element ${index + 1}:`, picture);

              // Create picture element (emoji icon or text-based)
              const pictureElement = document.createElement('span');

              // Check if it's a text-based picture (starts with [ and ends with ])
              if (picture.startsWith('[') && picture.endsWith(']')) {
                // Text-based picture like [LOGO], [MENU], etc.
                pictureElement.textContent = picture;
                pictureElement.style.backgroundColor = 'rgba(255,255,255,0.2)';
                pictureElement.style.border = '2px solid rgba(255,255,255,0.5)';
                pictureElement.style.borderRadius = '8px';
                pictureElement.style.padding = '8px 16px';
                pictureElement.style.fontSize = '0.9em';
                pictureElement.style.fontWeight = 'bold';
                pictureElement.style.textTransform = 'uppercase';
              } else {
                // Emoji-based picture icon
                pictureElement.textContent = picture;
                pictureElement.style.fontSize = '2em'; // Larger than regular emoji
                pictureElement.style.backgroundColor = 'rgba(255,255,255,0.1)';
                pictureElement.style.border = '2px solid rgba(255,255,255,0.3)';
                pictureElement.style.borderRadius = '50%';
                pictureElement.style.padding = '10px';
                pictureElement.style.width = '60px';
                pictureElement.style.height = '60px';
                pictureElement.style.textAlign = 'center';
                pictureElement.style.lineHeight = '40px';
              }

              // Common styling for all picture elements
              pictureElement.style.display = 'inline-block';
              pictureElement.style.margin = '0 25px';
              pictureElement.style.verticalAlign = 'middle';
              pictureElement.style.boxShadow = '2px 2px 8px rgba(0,0,0,0.3)';
              pictureElement.style.textShadow = '1px 1px 2px rgba(0,0,0,0.5)';

              content.appendChild(pictureElement);
              console.log(`Added picture element ${index + 1} to content`);
            }
          });
        } else {
          console.log('No picture elements to process');
        }

        // Add the same sequence again for continuous scrolling
        if (settings.text) {
          const textSpan2 = document.createElement('span');
          textSpan2.textContent = settings.text;
          textSpan2.style.marginLeft = '60px'; // Space before repeated text
          textSpan2.style.marginRight = '60px'; // Space after repeated text
          textSpan2.style.display = 'inline-block';
          content.appendChild(textSpan2);
        }

        // Add visual elements again for continuous scrolling
        if (settings.visuals && Array.isArray(settings.visuals) && settings.visuals.length > 0) {
          settings.visuals.forEach((visual, index) => {
            if (visual) {
              const visualElement = document.createElement('span');
              visualElement.textContent = visual;
              visualElement.style.display = 'inline-block';
              visualElement.style.margin = '0 30px';
              visualElement.style.fontSize = '1.2em';
              visualElement.style.verticalAlign = 'middle';
              visualElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
              visualElement.style.fontWeight = 'bold';
              content.appendChild(visualElement);
            }
          });
        }

        // Add picture elements again for continuous scrolling
        if (settings.pictures && Array.isArray(settings.pictures) && settings.pictures.length > 0) {
          settings.pictures.forEach((picture, index) => {
            if (picture) {
              const pictureElement = document.createElement('span');

              if (picture.startsWith('[') && picture.endsWith(']')) {
                // Text-based picture
                pictureElement.textContent = picture;
                pictureElement.style.backgroundColor = 'rgba(255,255,255,0.2)';
                pictureElement.style.border = '2px solid rgba(255,255,255,0.5)';
                pictureElement.style.borderRadius = '8px';
                pictureElement.style.padding = '8px 16px';
                pictureElement.style.fontSize = '0.9em';
                pictureElement.style.fontWeight = 'bold';
                pictureElement.style.textTransform = 'uppercase';
              } else {
                // Emoji-based picture icon
                pictureElement.textContent = picture;
                pictureElement.style.fontSize = '2em';
                pictureElement.style.backgroundColor = 'rgba(255,255,255,0.1)';
                pictureElement.style.border = '2px solid rgba(255,255,255,0.3)';
                pictureElement.style.borderRadius = '50%';
                pictureElement.style.padding = '10px';
                pictureElement.style.width = '60px';
                pictureElement.style.height = '60px';
                pictureElement.style.textAlign = 'center';
                pictureElement.style.lineHeight = '40px';
              }

              pictureElement.style.display = 'inline-block';
              pictureElement.style.margin = '0 25px';
              pictureElement.style.verticalAlign = 'middle';
              pictureElement.style.boxShadow = '2px 2px 8px rgba(0,0,0,0.3)';
              pictureElement.style.textShadow = '1px 1px 2px rgba(0,0,0,0.5)';

              content.appendChild(pictureElement);
            }
          });
        }

        // Update animation speed
        const speed = settings.scrollSpeed || 20;
        content.style.animation = `scroll ${speed}s linear infinite`;
      } catch (error) {
        console.error('Error applying settings:', error);
      }
    });
  </script>
</body>
</html>