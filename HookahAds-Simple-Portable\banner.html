<!DOCTYPE html>
<html>
<head>
  <title>Banner Display</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background-color: transparent;
    }
    #banner {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
    }
    #content {
      white-space: nowrap;
      position: absolute;
      left: 100%;
      animation: scroll 20s linear infinite;
      display: flex;
      align-items: center;
      height: 100%;
    }
    #content img {
      height: 80%;
      max-height: 150px;
      margin: 0 20px;
      vertical-align: middle;
      object-fit: contain;
    }
    @keyframes scroll {
      from { transform: translateX(0); }
      to { transform: translateX(calc(-100vw - 100%)); }
    }
  </style>
</head>
<body>
  <div id="banner">
    <div id="content"></div>
  </div>
  <script>
    const { ipcRenderer } = require('electron');

    ipcRenderer.on('apply-settings', (event, settings) => {
      try {
        const banner = document.getElementById('banner');
        const content = document.getElementById('content');

        // Apply banner height
        if (settings.height) {
          banner.style.height = settings.height + 'px';
        }

        // Apply colors
        if (settings.backgroundColor) {
          banner.style.backgroundColor = settings.backgroundColor;
        }
        if (settings.textColor) {
          content.style.color = settings.textColor;
        }

        // Apply font size (percentage of banner height) with stable alignment
        if (settings.fontSize && settings.height) {
          const fontSize = (settings.height * settings.fontSize / 100);
          content.style.fontSize = fontSize + 'px';
          content.style.lineHeight = settings.height + 'px';
          content.style.height = settings.height + 'px';
          content.style.display = 'flex';
          content.style.alignItems = 'center';

          // Store font size for other elements to use
          content.setAttribute('data-font-size', fontSize);
        }

        // Create simple sequence: Text → Images → Text → Images...
        content.innerHTML = '';

        console.log('Creating banner content with settings:', settings);

        // Add text first
        if (settings.text) {
          const textSpan = document.createElement('span');
          textSpan.textContent = settings.text;
          textSpan.style.marginRight = '60px'; // Space after text
          textSpan.style.display = 'inline-block';
          content.appendChild(textSpan);
          console.log('Added text:', settings.text);
        }

        // Add visual elements after text (emoji, symbols, decorative text)
        if (settings.visuals && Array.isArray(settings.visuals) && settings.visuals.length > 0) {
          console.log('Processing visual elements:', settings.visuals);

          settings.visuals.forEach((visual, index) => {
            if (visual) {
              console.log(`Creating visual element ${index + 1}:`, visual);

              // Create visual element - follows same font size as banner text
              const visualElement = document.createElement('span');
              visualElement.textContent = visual;
              visualElement.style.display = 'inline-block';
              visualElement.style.margin = '0 30px';
              visualElement.style.fontSize = 'inherit'; // Same as banner text
              visualElement.style.verticalAlign = 'middle';

              // Add some special styling for visual impact
              visualElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
              visualElement.style.fontWeight = 'bold';

              content.appendChild(visualElement);
              console.log(`Added visual element ${index + 1} to content`);
            }
          });
        } else {
          console.log('No visual elements to process');
        }

        // Add picture box after visual elements
        if (settings.picture) {
          console.log('Adding picture box');

          // Create square picture container (90% of banner height)
          const pictureContainer = document.createElement('div');
          pictureContainer.style.display = 'inline-block';
          pictureContainer.style.margin = '0 30px';
          pictureContainer.style.verticalAlign = 'middle';
          // Removed background, border, and box shadow - only invisible container

          // Make container size based on font size (80% of font size for better alignment)
          if (settings.fontSize && settings.height) {
            const fontSize = (settings.height * settings.fontSize / 100);
            const pictureSize = (fontSize * 0.8) + 'px'; // 80% of font size
            pictureContainer.style.width = pictureSize;
            pictureContainer.style.height = pictureSize;
          }
          pictureContainer.style.position = 'relative';
          pictureContainer.style.overflow = 'hidden';

          // Create image element that fits inside the square
          const imgElement = document.createElement('img');
          imgElement.src = settings.picture;
          imgElement.style.width = '100%';
          imgElement.style.height = '100%';
          imgElement.style.display = 'block';
          imgElement.style.objectFit = 'contain'; // Fits entire image inside square
          // Removed background color and border radius - pure image display

          imgElement.onload = function() {
            console.log('Picture box image loaded successfully');
          };

          imgElement.onerror = function() {
            console.log('Picture box image failed to load');
            pictureContainer.innerHTML = '<div style="color: #ff6666; padding: 20px; font-size: 14px;">Picture Error</div>';
          };

          pictureContainer.appendChild(imgElement);
          content.appendChild(pictureContainer);
          console.log('Picture box added to content');
        } else {
          console.log('No picture to display');
        }



        // Add the same sequence again for continuous scrolling
        if (settings.text) {
          const textSpan2 = document.createElement('span');
          textSpan2.textContent = settings.text;
          textSpan2.style.marginLeft = '60px'; // Space before repeated text
          textSpan2.style.marginRight = '60px'; // Space after repeated text
          textSpan2.style.display = 'inline-block';
          content.appendChild(textSpan2);
        }

        // Add visual elements again for continuous scrolling - follows same font size
        if (settings.visuals && Array.isArray(settings.visuals) && settings.visuals.length > 0) {
          settings.visuals.forEach((visual, index) => {
            if (visual) {
              const visualElement = document.createElement('span');
              visualElement.textContent = visual;
              visualElement.style.display = 'inline-block';
              visualElement.style.margin = '0 30px';
              visualElement.style.fontSize = 'inherit'; // Same as banner text
              visualElement.style.verticalAlign = 'middle';
              visualElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
              visualElement.style.fontWeight = 'bold';
              content.appendChild(visualElement);
            }
          });
        }

        // Add square picture box again for continuous scrolling
        if (settings.picture) {
          const pictureContainer = document.createElement('div');
          pictureContainer.style.display = 'inline-block';
          pictureContainer.style.margin = '0 30px';
          pictureContainer.style.verticalAlign = 'middle';
          // Removed background, border, and box shadow - only invisible container

          // Make container size based on font size (80% of font size for better alignment)
          if (settings.fontSize && settings.height) {
            const fontSize = (settings.height * settings.fontSize / 100);
            const pictureSize = (fontSize * 0.8) + 'px'; // 80% of font size
            pictureContainer.style.width = pictureSize;
            pictureContainer.style.height = pictureSize;
          }
          pictureContainer.style.position = 'relative';
          pictureContainer.style.overflow = 'hidden';

          const imgElement = document.createElement('img');
          imgElement.src = settings.picture;
          imgElement.style.width = '100%';
          imgElement.style.height = '100%';
          imgElement.style.display = 'block';
          imgElement.style.objectFit = 'contain';
          // Removed background color and border radius - pure image display

          imgElement.onerror = function() {
            pictureContainer.innerHTML = '<div style="color: #ff6666; padding: 20px; font-size: 14px;">Picture Error</div>';
          };

          pictureContainer.appendChild(imgElement);
          content.appendChild(pictureContainer);
        }



        // Update animation speed
        const speed = settings.scrollSpeed || 20;
        content.style.animation = `scroll ${speed}s linear infinite`;
      } catch (error) {
        console.error('Error applying settings:', error);
      }
    });
  </script>
</body>
</html>