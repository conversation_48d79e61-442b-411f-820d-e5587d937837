<!DOCTYPE html>
<html>
<head>
  <title>Banner Display</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background-color: transparent;
    }
    #banner {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
    }
    #content {
      white-space: nowrap;
      position: absolute;
      left: 100%;
      animation: scroll 20s linear infinite;
      display: flex;
      align-items: center;
      height: 100%;
    }
    #content img {
      height: 80%;
      max-height: 150px;
      margin: 0 20px;
      vertical-align: middle;
      object-fit: contain;
    }
    @keyframes scroll {
      from { transform: translateX(0); }
      to { transform: translateX(calc(-100vw - 100%)); }
    }
  </style>
</head>
<body>
  <div id="banner">
    <div id="content"></div>
  </div>
  <script>
    const { ipcRenderer } = require('electron');

    ipcRenderer.on('apply-settings', (event, settings) => {
      try {
        const banner = document.getElementById('banner');
        const content = document.getElementById('content');

        // Apply banner height
        if (settings.height) {
          banner.style.height = settings.height + 'px';
        }

        // Apply colors
        if (settings.backgroundColor) {
          banner.style.backgroundColor = settings.backgroundColor;
        }
        if (settings.textColor) {
          content.style.color = settings.textColor;
        }

        // Apply font size (percentage of banner height) with stable alignment
        if (settings.fontSize && settings.height) {
          const fontSize = (settings.height * settings.fontSize / 100);
          content.style.fontSize = fontSize + 'px';
          content.style.lineHeight = settings.height + 'px';
          content.style.height = settings.height + 'px';
          content.style.display = 'flex';
          content.style.alignItems = 'center';

          // Store font size for other elements to use
          content.setAttribute('data-font-size', fontSize);
        }

        // Create multiple sequences: Sequence1 → Sequence2 → Sequence3 → Sequence1...
        content.innerHTML = '';

        console.log('Creating banner content with settings:', settings);

        // Function to create a single sequence
        function createSequence(sequence, index) {
          console.log(`Creating sequence ${index + 1}:`, sequence);

          // Add text
          if (sequence.text) {
            const textSpan = document.createElement('span');
            textSpan.textContent = sequence.text;
            textSpan.style.marginRight = '40px';
            textSpan.style.display = 'inline-block';
            content.appendChild(textSpan);
          }

          // Add visual element (emoji)
          if (sequence.visualElement) {
            const visualElement = document.createElement('span');
            visualElement.textContent = sequence.visualElement;
            visualElement.style.display = 'inline-block';
            visualElement.style.margin = '0 20px';
            visualElement.style.fontSize = 'inherit';
            visualElement.style.verticalAlign = 'middle';
            visualElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
            visualElement.style.fontWeight = 'bold';
            content.appendChild(visualElement);
          }

          // Add custom visual text
          if (sequence.customVisual) {
            const customElement = document.createElement('span');
            customElement.textContent = sequence.customVisual;
            customElement.style.display = 'inline-block';
            customElement.style.margin = '0 20px';
            customElement.style.fontSize = 'inherit';
            customElement.style.verticalAlign = 'middle';
            customElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
            customElement.style.fontWeight = 'bold';
            content.appendChild(customElement);
          }

          // Add picture
          if (sequence.picture) {
            const pictureContainer = document.createElement('div');
            pictureContainer.style.display = 'inline-block';
            pictureContainer.style.margin = '0 30px';
            pictureContainer.style.verticalAlign = 'middle';

            if (settings.fontSize && settings.height) {
              const fontSize = (settings.height * settings.fontSize / 100);
              const pictureSize = (fontSize * 0.8) + 'px';
              pictureContainer.style.width = pictureSize;
              pictureContainer.style.height = pictureSize;
            }
            pictureContainer.style.position = 'relative';
            pictureContainer.style.overflow = 'hidden';

            const imgElement = document.createElement('img');
            imgElement.src = sequence.picture;
            imgElement.style.width = '100%';
            imgElement.style.height = '100%';
            imgElement.style.display = 'block';
            imgElement.style.objectFit = 'contain';

            pictureContainer.appendChild(imgElement);
            content.appendChild(pictureContainer);
          }

          // Add spacing after sequence
          const spacer = document.createElement('span');
          spacer.style.marginRight = '80px';
          spacer.textContent = ' ';
          content.appendChild(spacer);
        }

        // Create all sequences
        if (settings.sequences && settings.sequences.length > 0) {
          // Create sequences multiple times for continuous scrolling
          for (let repeat = 0; repeat < 3; repeat++) {
            settings.sequences.forEach((sequence, index) => {
              createSequence(sequence, index);
            });
          }
        }











        // Update animation speed
        const speed = settings.scrollSpeed || 20;
        content.style.animation = `scroll ${speed}s linear infinite`;
      } catch (error) {
        console.error('Error applying settings:', error);
      }
    });
  </script>
</body>
</html>