<!DOCTYPE html>
<html>
<head>
  <title>Banner Display</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background-color: transparent;
    }
    #banner {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
    }
    #content {
      white-space: nowrap;
      position: absolute;
      left: 100%;
      animation: scroll 20s linear infinite;
    }
    #content img {
      height: 80%;
      max-height: 150px;
      margin: 0 20px;
      vertical-align: middle;
      object-fit: contain;
    }
    @keyframes scroll {
      from { transform: translateX(0); }
      to { transform: translateX(calc(-100vw - 100%)); }
    }
  </style>
</head>
<body>
  <div id="banner">
    <div id="content"></div>
  </div>
  <script>
    const { ipcRenderer } = require('electron');

    ipcRenderer.on('apply-settings', (event, settings) => {
      try {
        const banner = document.getElementById('banner');
        const content = document.getElementById('content');

        // Apply banner height
        if (settings.height) {
          banner.style.height = settings.height + 'px';
        }

        // Apply colors
        if (settings.backgroundColor) {
          banner.style.backgroundColor = settings.backgroundColor;
        }
        if (settings.textColor) {
          content.style.color = settings.textColor;
        }

        // Apply font size (percentage of banner height)
        if (settings.fontSize && settings.height) {
          const fontSize = (settings.height * settings.fontSize / 100);
          content.style.fontSize = fontSize + 'px';
          content.style.lineHeight = settings.height + 'px';
        }

        // Create simple sequence: Text → Images → Text → Images...
        content.innerHTML = '';

        console.log('Creating banner content with settings:', settings);

        // Add text first
        if (settings.text) {
          const textSpan = document.createElement('span');
          textSpan.textContent = settings.text;
          textSpan.style.marginRight = '60px'; // Space after text
          textSpan.style.display = 'inline-block';
          content.appendChild(textSpan);
          console.log('Added text:', settings.text);
        }

        // Add visual elements after text (emoji, symbols, decorative text)
        if (settings.visuals && Array.isArray(settings.visuals) && settings.visuals.length > 0) {
          console.log('Processing visual elements:', settings.visuals);

          settings.visuals.forEach((visual, index) => {
            if (visual) {
              console.log(`Creating visual element ${index + 1}:`, visual);

              // Create visual element
              const visualElement = document.createElement('span');
              visualElement.textContent = visual;
              visualElement.style.display = 'inline-block';
              visualElement.style.margin = '0 30px';
              visualElement.style.fontSize = '1.2em'; // Slightly larger than text
              visualElement.style.verticalAlign = 'middle';

              // Add some special styling for visual impact
              visualElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
              visualElement.style.fontWeight = 'bold';

              content.appendChild(visualElement);
              console.log(`Added visual element ${index + 1} to content`);
            }
          });
        } else {
          console.log('No visual elements to process');
        }

        // Add pictures after visual elements
        if (settings.pictures && Array.isArray(settings.pictures) && settings.pictures.length > 0) {
          console.log('Processing pictures:', settings.pictures);

          settings.pictures.forEach((picture, index) => {
            if (picture) {
              console.log(`Creating picture ${index + 1}:`, picture);

              try {
                // Use Node.js to read the file and convert to base64
                const fs = require('fs');
                const path = require('path');

                // Check if file exists
                if (fs.existsSync(picture)) {
                  console.log('Picture file exists, reading:', picture);

                  // Read file as base64
                  const imageData = fs.readFileSync(picture);
                  const base64 = imageData.toString('base64');

                  // Determine MIME type from extension
                  const ext = path.extname(picture).toLowerCase();
                  let mimeType = 'image/jpeg'; // default
                  if (ext === '.png') mimeType = 'image/png';
                  else if (ext === '.gif') mimeType = 'image/gif';
                  else if (ext === '.bmp') mimeType = 'image/bmp';
                  else if (ext === '.webp') mimeType = 'image/webp';

                  // Create data URL
                  const dataUrl = `data:${mimeType};base64,${base64}`;

                  // Create image element
                  const imgElement = document.createElement('img');
                  imgElement.src = dataUrl;
                  imgElement.style.height = '80%';
                  imgElement.style.maxHeight = '150px';
                  imgElement.style.margin = '0 20px';
                  imgElement.style.verticalAlign = 'middle';
                  imgElement.style.objectFit = 'contain';
                  imgElement.style.display = 'inline-block';
                  imgElement.style.border = '2px solid rgba(255,255,255,0.3)';
                  imgElement.style.borderRadius = '8px';
                  imgElement.style.boxShadow = '2px 2px 8px rgba(0,0,0,0.3)';
                  imgElement.alt = `Picture ${index + 1}`;

                  imgElement.onload = function() {
                    console.log('Picture loaded successfully!');
                  };

                  imgElement.onerror = function() {
                    console.log('Picture failed to load');
                  };

                  content.appendChild(imgElement);
                  console.log(`Added picture ${index + 1} to content`);

                } else {
                  console.log('Picture file does not exist:', picture);
                  // Show placeholder for missing file
                  const placeholder = document.createElement('div');
                  placeholder.style.display = 'inline-block';
                  placeholder.style.width = '120px';
                  placeholder.style.height = '80px';
                  placeholder.style.backgroundColor = '#ffcccc';
                  placeholder.style.border = '2px dashed #ff6666';
                  placeholder.style.margin = '0 20px';
                  placeholder.style.textAlign = 'center';
                  placeholder.style.lineHeight = '80px';
                  placeholder.style.fontSize = '12px';
                  placeholder.style.color = '#cc0000';
                  placeholder.style.borderRadius = '8px';
                  placeholder.textContent = 'Picture Not Found';
                  placeholder.title = picture;
                  content.appendChild(placeholder);
                }

              } catch (error) {
                console.error('Error processing picture:', error);
                // Show error placeholder
                const errorPlaceholder = document.createElement('div');
                errorPlaceholder.style.display = 'inline-block';
                errorPlaceholder.style.width = '120px';
                errorPlaceholder.style.height = '80px';
                errorPlaceholder.style.backgroundColor = '#ffffcc';
                errorPlaceholder.style.border = '2px dashed #ffaa00';
                errorPlaceholder.style.margin = '0 20px';
                errorPlaceholder.style.textAlign = 'center';
                errorPlaceholder.style.lineHeight = '80px';
                errorPlaceholder.style.fontSize = '12px';
                errorPlaceholder.style.color = '#aa6600';
                errorPlaceholder.style.borderRadius = '8px';
                errorPlaceholder.textContent = 'Picture Error';
                errorPlaceholder.title = `Error: ${error.message}`;
                content.appendChild(errorPlaceholder);
              }
            }
          });
        } else {
          console.log('No pictures to process');
        }

        // Add the same sequence again for continuous scrolling
        if (settings.text) {
          const textSpan2 = document.createElement('span');
          textSpan2.textContent = settings.text;
          textSpan2.style.marginLeft = '60px'; // Space before repeated text
          textSpan2.style.marginRight = '60px'; // Space after repeated text
          textSpan2.style.display = 'inline-block';
          content.appendChild(textSpan2);
        }

        // Add visual elements again for continuous scrolling
        if (settings.visuals && Array.isArray(settings.visuals) && settings.visuals.length > 0) {
          settings.visuals.forEach((visual, index) => {
            if (visual) {
              const visualElement = document.createElement('span');
              visualElement.textContent = visual;
              visualElement.style.display = 'inline-block';
              visualElement.style.margin = '0 30px';
              visualElement.style.fontSize = '1.2em';
              visualElement.style.verticalAlign = 'middle';
              visualElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
              visualElement.style.fontWeight = 'bold';
              content.appendChild(visualElement);
            }
          });
        }

        // Add pictures again for continuous scrolling
        if (settings.pictures && Array.isArray(settings.pictures) && settings.pictures.length > 0) {
          settings.pictures.forEach((picture, index) => {
            if (picture) {
              try {
                const fs = require('fs');
                const path = require('path');

                if (fs.existsSync(picture)) {
                  const imageData = fs.readFileSync(picture);
                  const base64 = imageData.toString('base64');

                  const ext = path.extname(picture).toLowerCase();
                  let mimeType = 'image/jpeg';
                  if (ext === '.png') mimeType = 'image/png';
                  else if (ext === '.gif') mimeType = 'image/gif';
                  else if (ext === '.bmp') mimeType = 'image/bmp';
                  else if (ext === '.webp') mimeType = 'image/webp';

                  const dataUrl = `data:${mimeType};base64,${base64}`;

                  const imgElement = document.createElement('img');
                  imgElement.src = dataUrl;
                  imgElement.style.height = '80%';
                  imgElement.style.maxHeight = '150px';
                  imgElement.style.margin = '0 20px';
                  imgElement.style.verticalAlign = 'middle';
                  imgElement.style.objectFit = 'contain';
                  imgElement.style.display = 'inline-block';
                  imgElement.style.border = '2px solid rgba(255,255,255,0.3)';
                  imgElement.style.borderRadius = '8px';
                  imgElement.style.boxShadow = '2px 2px 8px rgba(0,0,0,0.3)';
                  imgElement.alt = `Picture ${index + 1}`;

                  content.appendChild(imgElement);
                } else {
                  // Show placeholder for missing file
                  const placeholder = document.createElement('div');
                  placeholder.style.display = 'inline-block';
                  placeholder.style.width = '120px';
                  placeholder.style.height = '80px';
                  placeholder.style.backgroundColor = '#ffcccc';
                  placeholder.style.border = '2px dashed #ff6666';
                  placeholder.style.margin = '0 20px';
                  placeholder.style.textAlign = 'center';
                  placeholder.style.lineHeight = '80px';
                  placeholder.style.fontSize = '12px';
                  placeholder.style.color = '#cc0000';
                  placeholder.style.borderRadius = '8px';
                  placeholder.textContent = 'Picture Not Found';
                  content.appendChild(placeholder);
                }
              } catch (error) {
                // Show error placeholder
                const errorPlaceholder = document.createElement('div');
                errorPlaceholder.style.display = 'inline-block';
                errorPlaceholder.style.width = '120px';
                errorPlaceholder.style.height = '80px';
                errorPlaceholder.style.backgroundColor = '#ffffcc';
                errorPlaceholder.style.border = '2px dashed #ffaa00';
                errorPlaceholder.style.margin = '0 20px';
                errorPlaceholder.style.textAlign = 'center';
                errorPlaceholder.style.lineHeight = '80px';
                errorPlaceholder.style.fontSize = '12px';
                errorPlaceholder.style.color = '#aa6600';
                errorPlaceholder.style.borderRadius = '8px';
                errorPlaceholder.textContent = 'Picture Error';
                content.appendChild(errorPlaceholder);
              }
            }
          });
        }

        // Update animation speed
        const speed = settings.scrollSpeed || 20;
        content.style.animation = `scroll ${speed}s linear infinite`;
      } catch (error) {
        console.error('Error applying settings:', error);
      }
    });
  </script>
</body>
</html>