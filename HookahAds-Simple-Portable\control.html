<!DOCTYPE html>
<html>
<head>
  <title>Banner Control Panel</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    .control-group {
      margin-bottom: 20px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input, button {
      margin: 5px 0;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    button {
      background-color: #007bff;
      color: white;
      cursor: pointer;
      font-size: 16px;
      padding: 10px 20px;
    }
    button:hover {
      background-color: #0056b3;
    }
    input[type="range"] {
      width: 200px;
    }
  </style>
</head>
<body>
  <div class="control-group">
    <label>Banner Text:</label>
    <input type="text" id="text" style="width: 100%">
  </div>

  <div class="control-group">
    <label>Banner Height (2cm - 8cm):</label>
    <input type="range" id="height" min="75" max="300" value="75">
    <span id="heightValue">2cm</span>
  </div>

  <div class="control-group">
    <label>Background Color:</label>
    <input type="color" id="backgroundColor" value="#000000">
  </div>

  <div class="control-group">
    <label>Text Color:</label>
    <input type="color" id="textColor" value="#ffffff">
  </div>

  <div class="control-group">
    <label>Font Size:</label>
    <select id="fontSize" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="50">Small (50% of banner height)</option>
      <option value="60">Medium-Small (60% of banner height)</option>
      <option value="70" selected>Medium (70% of banner height)</option>
      <option value="80">Medium-Large (80% of banner height)</option>
      <option value="90">Large (90% of banner height)</option>
    </select>
  </div>

  <div class="control-group">
    <label>Scroll Speed (seconds per cycle):</label>
    <input type="range" id="scrollSpeed" min="10" max="60" value="20">
    <span id="speedValue">20s</span>
  </div>

  <div class="control-group">
    <label>Visual Elements (instead of images):</label>
    <select id="visualElement" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="">No visual element</option>
      <option value="🔥">🔥 Fire (Hot/Popular)</option>
      <option value="⭐">⭐ Star (Premium/Quality)</option>
      <option value="🎉">🎉 Party (Celebration/Fun)</option>
      <option value="💎">💎 Diamond (Luxury/VIP)</option>
      <option value="🌟">🌟 Sparkle (Special/New)</option>
      <option value="🏆">🏆 Trophy (Best/Winner)</option>
      <option value="❤️">❤️ Heart (Love/Favorite)</option>
      <option value="🎯">🎯 Target (Special Offer)</option>
      <option value="⚡">⚡ Lightning (Fast/Energy)</option>
      <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
      <option value="🌙">🌙 Moon (Night/Evening)</option>
      <option value="☀️">☀️ Sun (Day/Bright)</option>
      <option value="🎵">🎵 Music (Entertainment)</option>
      <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
      <option value="💫">💫 Dizzy (Amazing/Wow)</option>
    </select>
    <div style="margin-top: 10px; font-size: 12px; color: #666;">
      Choose emoji/symbols that will appear after your text
    </div>
  </div>

  <div class="control-group">
    <label>Custom Visual Text:</label>
    <input type="text" id="customVisual" placeholder="Enter custom symbols, emoji, or decorative text" style="width: 300px;">
    <div style="margin-top: 5px; font-size: 12px; color: #666;">
      Examples: ★★★ PREMIUM ★★★, 🔥🔥🔥, ═══ SPECIAL ═══
    </div>
  </div>

  <div class="control-group">
    <label>Built-in Picture Icons:</label>
    <select id="pictureIcon" style="width: 250px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="">No picture icon</option>
      <option value="🏪">🏪 Store/Shop</option>
      <option value="🍃">🍃 Hookah/Shisha</option>
      <option value="💨">💨 Smoke/Vapor</option>
      <option value="🌿">🌿 Tobacco/Herbs</option>
      <option value="🔥">🔥 Hot Coals</option>
      <option value="🥤">🥤 Drinks/Beverages</option>
      <option value="🎵">🎵 Music/Entertainment</option>
      <option value="🌙">🌙 Night Scene</option>
      <option value="👥">👥 Friends/Social</option>
      <option value="🎪">🎪 Lounge/Party</option>
      <option value="💎">💎 Premium/VIP</option>
      <option value="⭐">⭐ Quality/Rating</option>
      <option value="🎯">🎯 Special Offer</option>
      <option value="📍">📍 Location/Address</option>
      <option value="📞">📞 Contact/Phone</option>
    </select>
    <div style="margin-top: 10px; font-size: 12px; color: #666;">
      Choose picture-style icons that represent your business visually
    </div>
  </div>

  <div class="control-group">
    <label>Custom Picture Text:</label>
    <input type="text" id="customPicture" placeholder="Create text-based pictures like [LOGO], [MENU], [VIP ROOM]" style="width: 350px;">
    <div style="margin-top: 5px; font-size: 12px; color: #666;">
      Examples: [LOGO], [MENU], [VIP ROOM], [NEW FLAVORS], [HAPPY HOUR]
    </div>
  </div>

  <button onclick="applySettings()">Apply Changes</button>

  <script>
    const { ipcRenderer } = require('electron');
    let selectedImages = [];

    // Height slider
    const heightSlider = document.getElementById('height');
    const heightValue = document.getElementById('heightValue');
    heightSlider.oninput = () => {
      const cm = (heightSlider.value / 37.5).toFixed(1);
      heightValue.textContent = cm + 'cm';
    };

    // Speed slider
    const speedSlider = document.getElementById('scrollSpeed');
    const speedValue = document.getElementById('speedValue');
    speedSlider.oninput = () => {
      speedValue.textContent = speedSlider.value + 's';
    };

    // Visual elements handling
    let selectedVisuals = [];
    let selectedPictureElements = [];

    function applySettings() {
      try {
        // Collect visual elements
        const visualElements = [];

        // Add selected emoji/symbol
        const selectedEmoji = document.getElementById('visualElement').value;
        if (selectedEmoji) {
          visualElements.push(selectedEmoji);
        }

        // Add custom visual text
        const customVisual = document.getElementById('customVisual').value.trim();
        if (customVisual) {
          visualElements.push(customVisual);
        }

        // Collect picture elements
        const pictureElements = [];

        // Add selected picture icon
        const selectedPictureIcon = document.getElementById('pictureIcon').value;
        if (selectedPictureIcon) {
          pictureElements.push(selectedPictureIcon);
        }

        // Add custom picture text
        const customPicture = document.getElementById('customPicture').value.trim();
        if (customPicture) {
          pictureElements.push(customPicture);
        }

        const settings = {
          text: document.getElementById('text').value || '',
          height: parseInt(document.getElementById('height').value) || 75,
          backgroundColor: document.getElementById('backgroundColor').value || '#000000',
          textColor: document.getElementById('textColor').value || '#ffffff',
          fontSize: parseInt(document.getElementById('fontSize').value) || 70,
          scrollSpeed: parseInt(document.getElementById('scrollSpeed').value) || 20,
          visuals: visualElements,
          pictures: pictureElements
        };

        ipcRenderer.send('update-settings', settings);

        // Visual feedback
        const button = document.querySelector('button');
        const originalText = button.textContent;
        button.textContent = 'Applied!';
        button.style.backgroundColor = '#4CAF50';
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = '';
        }, 1000);
      } catch (error) {
        console.error('Error applying settings:', error);
        alert('Error applying settings. Please try again.');
      }
    }
  </script>
</body>
</html>