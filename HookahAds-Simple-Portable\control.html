<!DOCTYPE html>
<html>
<head>
  <title>Banner Control Panel</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    .control-group {
      margin-bottom: 20px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input, button {
      margin: 5px 0;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    button {
      background-color: #007bff;
      color: white;
      cursor: pointer;
      font-size: 16px;
      padding: 10px 20px;
    }
    button:hover {
      background-color: #0056b3;
    }
    input[type="range"] {
      width: 200px;
    }
  </style>
</head>
<body>
  <div class="control-group">
    <label>Templates:</label>
    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
      <button onclick="loadTemplate(1)" style="padding: 8px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">📋 Load Template 1</button>
      <button onclick="loadTemplate(2)" style="padding: 8px 15px; background-color: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">📋 Load Template 2</button>
      <button onclick="loadTemplate(3)" style="padding: 8px 15px; background-color: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">📋 Load Template 3</button>
    </div>
    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
      <button onclick="saveTemplate(1)" style="padding: 8px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">💾 Save Template 1</button>
      <button onclick="saveTemplate(2)" style="padding: 8px 15px; background-color: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">💾 Save Template 2</button>
      <button onclick="saveTemplate(3)" style="padding: 8px 15px; background-color: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">💾 Save Template 3</button>
    </div>
    <div id="templateStatus" style="margin-top: 5px; font-size: 12px; color: #666;">
      Templates: Save your current settings or load previously saved configurations
    </div>
  </div>

  <div class="control-group">
    <label>Number of Sequences:</label>
    <select id="sequenceCount" onchange="updateSequenceInputs()" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="1">1 Sequence</option>
      <option value="2">2 Sequences</option>
      <option value="3">3 Sequences</option>
    </select>
    <div style="margin-top: 5px; font-size: 12px; color: #666;">
      Choose how many different advertisement sequences you want
    </div>
  </div>

  <!-- Sequence 1 -->
  <div class="sequence-section" id="sequence1" style="border: 2px solid #4CAF50; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #f9fff9;">
    <h3 style="margin-top: 0; color: #4CAF50;">📝 Sequence 1</h3>
    <div class="control-group">
      <label>Text:</label>
      <input type="text" id="text1" placeholder="Enter text for sequence 1" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Visual Element:</label>
      <select id="visualElement1" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">No visual element</option>
        <option value="🔥">🔥 Fire (Hot/Popular)</option>
        <option value="⭐">⭐ Star (Premium/Quality)</option>
        <option value="🎉">🎉 Party (Celebration/Fun)</option>
        <option value="💎">💎 Diamond (Luxury/VIP)</option>
        <option value="🌟">🌟 Sparkle (Special/New)</option>
        <option value="🏆">🏆 Trophy (Best/Winner)</option>
        <option value="❤️">❤️ Heart (Love/Favorite)</option>
        <option value="🎯">🎯 Target (Special Offer)</option>
        <option value="⚡">⚡ Lightning (Fast/Energy)</option>
        <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
        <option value="🌙">🌙 Moon (Night/Evening)</option>
        <option value="☀️">☀️ Sun (Day/Bright)</option>
        <option value="🎵">🎵 Music (Entertainment)</option>
        <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
        <option value="💫">💫 Dizzy (Amazing/Wow)</option>
      </select>
    </div>
    <div class="control-group">
      <label>Custom Visual Text:</label>
      <input type="text" id="customVisual1" placeholder="★★★ PREMIUM ★★★, ═══ SPECIAL ═══" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Picture:</label>
      <input type="file" id="pictureUpload1" accept="image/*">
      <button onclick="clearPicture(1)" style="margin-left: 10px; padding: 5px 10px;">Clear</button>
      <div id="picturePreview1" style="margin-top: 10px; padding: 10px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; min-height: 60px; background-color: #f9f9f9; font-size: 12px; color: #666;">
        No picture uploaded
      </div>
    </div>
  </div>

  <!-- Sequence 2 (hidden by default) -->
  <div class="sequence-section" id="sequence2" style="border: 2px solid #2196F3; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #f9f9ff; display: none;">
    <h3 style="margin-top: 0; color: #2196F3;">📝 Sequence 2</h3>
    <div class="control-group">
      <label>Text:</label>
      <input type="text" id="text2" placeholder="Enter text for sequence 2" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Visual Element:</label>
      <select id="visualElement2" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">No visual element</option>
        <option value="🔥">🔥 Fire (Hot/Popular)</option>
        <option value="⭐">⭐ Star (Premium/Quality)</option>
        <option value="🎉">🎉 Party (Celebration/Fun)</option>
        <option value="💎">💎 Diamond (Luxury/VIP)</option>
        <option value="🌟">🌟 Sparkle (Special/New)</option>
        <option value="🏆">🏆 Trophy (Best/Winner)</option>
        <option value="❤️">❤️ Heart (Love/Favorite)</option>
        <option value="🎯">🎯 Target (Special Offer)</option>
        <option value="⚡">⚡ Lightning (Fast/Energy)</option>
        <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
        <option value="🌙">🌙 Moon (Night/Evening)</option>
        <option value="☀️">☀️ Sun (Day/Bright)</option>
        <option value="🎵">🎵 Music (Entertainment)</option>
        <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
        <option value="💫">💫 Dizzy (Amazing/Wow)</option>
      </select>
    </div>
    <div class="control-group">
      <label>Custom Visual Text:</label>
      <input type="text" id="customVisual2" placeholder="★★★ PREMIUM ★★★, ═══ SPECIAL ═══" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Picture:</label>
      <input type="file" id="pictureUpload2" accept="image/*">
      <button onclick="clearPicture(2)" style="margin-left: 10px; padding: 5px 10px;">Clear</button>
      <div id="picturePreview2" style="margin-top: 10px; padding: 10px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; min-height: 60px; background-color: #f9f9f9; font-size: 12px; color: #666;">
        No picture uploaded
      </div>
    </div>
  </div>

  <!-- Sequence 3 (hidden by default) -->
  <div class="sequence-section" id="sequence3" style="border: 2px solid #FF9800; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #fffaf9; display: none;">
    <h3 style="margin-top: 0; color: #FF9800;">📝 Sequence 3</h3>
    <div class="control-group">
      <label>Text:</label>
      <input type="text" id="text3" placeholder="Enter text for sequence 3" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Visual Element:</label>
      <select id="visualElement3" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">No visual element</option>
        <option value="🔥">🔥 Fire (Hot/Popular)</option>
        <option value="⭐">⭐ Star (Premium/Quality)</option>
        <option value="🎉">🎉 Party (Celebration/Fun)</option>
        <option value="💎">💎 Diamond (Luxury/VIP)</option>
        <option value="🌟">🌟 Sparkle (Special/New)</option>
        <option value="🏆">🏆 Trophy (Best/Winner)</option>
        <option value="❤️">❤️ Heart (Love/Favorite)</option>
        <option value="🎯">🎯 Target (Special Offer)</option>
        <option value="⚡">⚡ Lightning (Fast/Energy)</option>
        <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
        <option value="🌙">🌙 Moon (Night/Evening)</option>
        <option value="☀️">☀️ Sun (Day/Bright)</option>
        <option value="🎵">🎵 Music (Entertainment)</option>
        <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
        <option value="💫">💫 Dizzy (Amazing/Wow)</option>
      </select>
    </div>
    <div class="control-group">
      <label>Custom Visual Text:</label>
      <input type="text" id="customVisual3" placeholder="★★★ PREMIUM ★★★, ═══ SPECIAL ═══" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Picture:</label>
      <input type="file" id="pictureUpload3" accept="image/*">
      <button onclick="clearPicture(3)" style="margin-left: 10px; padding: 5px 10px;">Clear</button>
      <div id="picturePreview3" style="margin-top: 10px; padding: 10px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; min-height: 60px; background-color: #f9f9f9; font-size: 12px; color: #666;">
        No picture uploaded
      </div>
    </div>
  </div>

  <div class="control-group">
    <label>Banner Height (2cm - 8cm):</label>
    <input type="range" id="height" min="75" max="300" value="75">
    <span id="heightValue">2cm</span>
  </div>

  <div class="control-group">
    <label>Background Color:</label>
    <input type="color" id="backgroundColor" value="#000000">
  </div>

  <div class="control-group">
    <label>Text Color:</label>
    <input type="color" id="textColor" value="#ffffff">
  </div>

  <div class="control-group">
    <label>Font Size:</label>
    <select id="fontSize" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="50">Small (50% of banner height)</option>
      <option value="60">Medium-Small (60% of banner height)</option>
      <option value="70" selected>Medium (70% of banner height)</option>
      <option value="80">Medium-Large (80% of banner height)</option>
      <option value="90">Large (90% of banner height)</option>
    </select>
  </div>

  <div class="control-group">
    <label>Scroll Speed (seconds per cycle):</label>
    <input type="range" id="scrollSpeed" min="15" max="120" value="30">
    <span id="speedValue">30s</span>
    <div style="margin-top: 5px; font-size: 12px; color: #666;">
      15s = Fast • 30s = Normal • 60s = Slow • 120s = Very Slow
    </div>
  </div>





  <button onclick="applySettings()">Apply Changes</button>

  <script>
    const { ipcRenderer } = require('electron');
    let selectedImages = [];

    // Height slider
    const heightSlider = document.getElementById('height');
    const heightValue = document.getElementById('heightValue');
    heightSlider.oninput = () => {
      const cm = (heightSlider.value / 37.5).toFixed(1);
      heightValue.textContent = cm + 'cm';
    };

    // Speed slider
    const speedSlider = document.getElementById('scrollSpeed');
    const speedValue = document.getElementById('speedValue');
    speedSlider.oninput = () => {
      speedValue.textContent = speedSlider.value + 's';
    };

    // Multiple sequences handling
    let sequences = [
      { text: '', visualElement: '', customVisual: '', picture: null },
      { text: '', visualElement: '', customVisual: '', picture: null },
      { text: '', visualElement: '', customVisual: '', picture: null }
    ];

    // Show/hide sequence inputs based on count
    function updateSequenceInputs() {
      const count = parseInt(document.getElementById('sequenceCount').value);

      for (let i = 1; i <= 3; i++) {
        const sequenceDiv = document.getElementById(`sequence${i}`);
        if (i <= count) {
          sequenceDiv.style.display = 'block';
        } else {
          sequenceDiv.style.display = 'none';
        }
      }
    }

    // Picture upload handling for multiple sequences
    function setupPictureUpload(sequenceNum) {
      document.getElementById(`pictureUpload${sequenceNum}`).onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function(event) {
            sequences[sequenceNum - 1].picture = event.target.result;

            // Show preview
            const preview = document.getElementById(`picturePreview${sequenceNum}`);
            preview.innerHTML = `
              <img src="${sequences[sequenceNum - 1].picture}" style="max-width: 150px; max-height: 60px; border-radius: 4px;">
              <div style="margin-top: 5px; font-size: 10px; color: #4CAF50; font-weight: bold;">${file.name}</div>
            `;
          };
          reader.readAsDataURL(file);
        }
      };
    }

    // Clear picture function for sequences
    function clearPicture(sequenceNum) {
      sequences[sequenceNum - 1].picture = null;
      document.getElementById(`pictureUpload${sequenceNum}`).value = '';
      document.getElementById(`picturePreview${sequenceNum}`).innerHTML = 'No picture uploaded';
    }

    // Setup picture uploads for all sequences
    setupPictureUpload(1);
    setupPictureUpload(2);
    setupPictureUpload(3);

    // Template management
    let templates = {
      1: null,
      2: null,
      3: null
    };

    // Load templates from localStorage on startup
    function loadTemplatesFromStorage() {
      try {
        const savedTemplates = localStorage.getItem('hookahAdsTemplates');
        if (savedTemplates) {
          templates = JSON.parse(savedTemplates);
          console.log('Templates loaded from storage');
        } else {
          // Create default templates
          createDefaultTemplates();
        }
      } catch (error) {
        console.log('Error loading templates, creating defaults');
        createDefaultTemplates();
      }
    }

    // Create default templates
    function createDefaultTemplates() {
      templates = {
        1: {
          name: "Store Hours & Info",
          sequenceCount: 2,
          height: 100,
          backgroundColor: "#000000",
          textColor: "#ffffff",
          fontSize: 70,
          scrollSpeed: 45,
          sequences: [
            {
              text: "Open Daily 12PM-2AM • Premium Hookah Experience",
              visualElement: "🌙",
              customVisual: "✦✧✦ OPEN LATE ✦✧✦",
              picture: null
            },
            {
              text: "Best Quality Tobacco • Relaxing Atmosphere",
              visualElement: "🍃",
              customVisual: "★★★ PREMIUM ★★★",
              picture: null
            }
          ]
        },
        2: {
          name: "Special Offers",
          sequenceCount: 3,
          height: 100,
          backgroundColor: "#000000",
          textColor: "#ffffff",
          fontSize: 70,
          scrollSpeed: 45,
          sequences: [
            {
              text: "Happy Hour 5-7PM • 50% Off All Hookah",
              visualElement: "🎉",
              customVisual: "★★★ SPECIAL OFFER ★★★",
              picture: null
            },
            {
              text: "Weekend Special • Live DJ Saturday",
              visualElement: "🎵",
              customVisual: "♪♫♪ LIVE MUSIC ♪♫♪",
              picture: null
            },
            {
              text: "Group Discounts Available • Call Now",
              visualElement: "👥",
              customVisual: "═══ GROUP DEALS ═══",
              picture: null
            }
          ]
        },
        3: {
          name: "VIP & Premium Services",
          sequenceCount: 2,
          height: 100,
          backgroundColor: "#000000",
          textColor: "#ffffff",
          fontSize: 70,
          scrollSpeed: 45,
          sequences: [
            {
              text: "VIP Lounge Available • Private Rooms",
              visualElement: "💎",
              customVisual: "◆◇◆ LUXURY EXPERIENCE ◆◇◆",
              picture: null
            },
            {
              text: "Premium Flavors • Exclusive Blends",
              visualElement: "🏆",
              customVisual: "▶ PREMIUM QUALITY ◀",
              picture: null
            }
          ]
        }
      };
      saveTemplatesToStorage();
    }

    // Save templates to localStorage
    function saveTemplatesToStorage() {
      try {
        localStorage.setItem('hookahAdsTemplates', JSON.stringify(templates));
        console.log('Templates saved to storage');
      } catch (error) {
        console.error('Error saving templates:', error);
      }
    }

    // Load a template
    function loadTemplate(templateNum) {
      const template = templates[templateNum];
      if (!template) {
        showTemplateStatus(`Template ${templateNum} not found`, 'error');
        return;
      }

      try {
        // Load basic settings
        document.getElementById('sequenceCount').value = template.sequenceCount;
        document.getElementById('height').value = template.height;
        document.getElementById('backgroundColor').value = template.backgroundColor;
        document.getElementById('textColor').value = template.textColor;
        document.getElementById('fontSize').value = template.fontSize;
        document.getElementById('scrollSpeed').value = template.scrollSpeed;

        // Update sequence inputs visibility
        updateSequenceInputs();

        // Load sequences
        template.sequences.forEach((seq, index) => {
          const seqNum = index + 1;
          if (seqNum <= template.sequenceCount) {
            document.getElementById(`text${seqNum}`).value = seq.text || '';
            document.getElementById(`visualElement${seqNum}`).value = seq.visualElement || '';
            document.getElementById(`customVisual${seqNum}`).value = seq.customVisual || '';

            // Clear picture preview
            document.getElementById(`picturePreview${seqNum}`).innerHTML = 'No picture uploaded';
            sequences[index].picture = seq.picture;
          }
        });

        showTemplateStatus(`Template ${templateNum} loaded: ${template.name}`, 'success');
      } catch (error) {
        showTemplateStatus(`Error loading template ${templateNum}`, 'error');
        console.error('Error loading template:', error);
      }
    }

    // Save current settings as template
    function saveTemplate(templateNum) {
      try {
        // Collect current settings
        const sequenceCount = parseInt(document.getElementById('sequenceCount').value);
        const currentSequences = [];

        for (let i = 1; i <= sequenceCount; i++) {
          currentSequences.push({
            text: document.getElementById(`text${i}`).value || '',
            visualElement: document.getElementById(`visualElement${i}`).value || '',
            customVisual: document.getElementById(`customVisual${i}`).value || '',
            picture: sequences[i - 1].picture
          });
        }

        const templateName = prompt(`Enter name for Template ${templateNum}:`, templates[templateNum]?.name || `Template ${templateNum}`);
        if (templateName === null) return; // User cancelled

        templates[templateNum] = {
          name: templateName,
          sequenceCount: sequenceCount,
          height: parseInt(document.getElementById('height').value),
          backgroundColor: document.getElementById('backgroundColor').value,
          textColor: document.getElementById('textColor').value,
          fontSize: parseInt(document.getElementById('fontSize').value),
          scrollSpeed: parseInt(document.getElementById('scrollSpeed').value),
          sequences: currentSequences
        };

        saveTemplatesToStorage();
        showTemplateStatus(`Template ${templateNum} saved: ${templateName}`, 'success');
      } catch (error) {
        showTemplateStatus(`Error saving template ${templateNum}`, 'error');
        console.error('Error saving template:', error);
      }
    }

    // Show template status message
    function showTemplateStatus(message, type) {
      const statusDiv = document.getElementById('templateStatus');
      statusDiv.textContent = message;
      statusDiv.style.color = type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#666';

      // Reset to default after 3 seconds
      setTimeout(() => {
        statusDiv.textContent = 'Templates: Save your current settings or load previously saved configurations';
        statusDiv.style.color = '#666';
      }, 3000);
    }

    // Initialize templates on page load
    loadTemplatesFromStorage();

    function applySettings() {
      try {
        // Collect data from active sequences
        const sequenceCount = parseInt(document.getElementById('sequenceCount').value);
        const activeSequences = [];

        for (let i = 1; i <= sequenceCount; i++) {
          const sequence = {
            text: document.getElementById(`text${i}`).value || '',
            visualElement: document.getElementById(`visualElement${i}`).value || '',
            customVisual: document.getElementById(`customVisual${i}`).value.trim() || '',
            picture: sequences[i - 1].picture
          };

          // Only add sequence if it has at least text
          if (sequence.text) {
            activeSequences.push(sequence);
          }
        }

        const settings = {
          height: parseInt(document.getElementById('height').value) || 75,
          backgroundColor: document.getElementById('backgroundColor').value || '#000000',
          textColor: document.getElementById('textColor').value || '#ffffff',
          fontSize: parseInt(document.getElementById('fontSize').value) || 70,
          scrollSpeed: parseInt(document.getElementById('scrollSpeed').value) || 20,
          sequences: activeSequences
        };

        ipcRenderer.send('update-settings', settings);

        // Visual feedback
        const button = document.querySelector('button');
        const originalText = button.textContent;
        button.textContent = 'Applied!';
        button.style.backgroundColor = '#4CAF50';
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = '';
        }, 1000);
      } catch (error) {
        console.error('Error applying settings:', error);
        alert('Error applying settings. Please try again.');
      }
    }
  </script>
</body>
</html>