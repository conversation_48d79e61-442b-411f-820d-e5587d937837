{"version": 3, "file": "resolve.js", "sourceRoot": "", "sources": ["../../src/util/resolve.ts"], "names": [], "mappings": ";;AAKA,sCAiBC;AAED,0CAyBC;AAjDD,8CAA0C;AAC1C,iCAAyB;AACzB,6BAA4B;AAC5B,6BAAmC;AAE5B,KAAK,UAAU,aAAa,CAAI,IAAwB,EAAE,IAAY;;IAC3E,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAA;IAClD,MAAM,YAAY,GAAG,IAAI,KAAK,QAAQ,CAAA;IACtC,IAAI,CAAC;QACH,IAAI,SAAS,KAAK,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK,IAAI,YAAY,CAAC,EAAE,CAAC;YAClE,MAAM,OAAO,GAAG,IAAA,mBAAa,EAAC,IAAI,CAAC,CAAC,IAAI,CAAA;YACxC,OAAO,MAAM,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,IAAI,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,SAAG,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,MAAA,KAAK,CAAC,OAAO,mCAAI,KAAK,CAAC,KAAK,EAAE,EAAE,0DAA0D,CAAC,CAAA;IACpI,CAAC;IACD,IAAI,CAAC;QACH,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;IACtB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,SAAG,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,MAAA,KAAK,CAAC,OAAO,mCAAI,KAAK,CAAC,KAAK,EAAE,EAAE,qBAAqB,CAAC,CAAA;QAC7F,MAAM,IAAI,KAAK,CAAC,MAAA,KAAK,CAAC,OAAO,mCAAI,KAAK,CAAC,KAAK,CAAC,CAAA;IAC/C,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,eAAe,CAAI,IAAwB,EAAE,QAAoB,EAAE,IAAY;IACnG,IAAI,QAAQ,IAAI,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACrD,oDAAoD;QACpD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,IAAI,CAAC,GAAG,QAAkB,CAAA;IAC1B,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACtB,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACrB,CAAC;IAED,IAAI,CAAC;QACH,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACxB,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,IAAA,eAAK,EAAC,CAAC,CAAC,CAAA;QACR,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACrB,CAAC;IAED,MAAM,CAAC,GAAQ,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;IAC3C,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;IAC3B,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC,CAAA;IACvB,CAAC;SAAM,CAAC;QACN,OAAO,WAAW,CAAA;IACpB,CAAC;AACH,CAAC", "sourcesContent": ["import { log } from \"builder-util/out/log\"\nimport debug from \"debug\"\nimport * as path from \"path\"\nimport { pathToFileURL } from \"url\"\n\nexport async function resolveModule<T>(type: string | undefined, name: string): Promise<T> {\n  const extension = path.extname(name).toLowerCase()\n  const isModuleType = type === \"module\"\n  try {\n    if (extension === \".mjs\" || (extension === \".js\" && isModuleType)) {\n      const fileUrl = pathToFileURL(name).href\n      return await eval(\"import ('\" + fileUrl + \"')\")\n    }\n  } catch (error: any) {\n    log.debug({ moduleName: name, message: error.message ?? error.stack }, \"Unable to dynamically import , falling back to `require`\")\n  }\n  try {\n    return require(name)\n  } catch (error: any) {\n    log.error({ moduleName: name, message: error.message ?? error.stack }, \"Unable to `require`\")\n    throw new Error(error.message ?? error.stack)\n  }\n}\n\nexport async function resolveFunction<T>(type: string | undefined, executor: T | string, name: string): Promise<T> {\n  if (executor == null || typeof executor !== \"string\") {\n    // is already function or explicitly ignored by user\n    return executor\n  }\n\n  let p = executor as string\n  if (p.startsWith(\".\")) {\n    p = path.resolve(p)\n  }\n\n  try {\n    p = require.resolve(p)\n  } catch (e: any) {\n    debug(e)\n    p = path.resolve(p)\n  }\n\n  const m: any = await resolveModule(type, p)\n  const namedExport = m[name]\n  if (namedExport == null) {\n    return m.default || m\n  } else {\n    return namedExport\n  }\n}\n"]}