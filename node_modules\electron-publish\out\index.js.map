{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAIA,2DAAyD;AAAhD,wHAAA,kBAAkB,OAAA;AAC3B,qDAAmD;AAA1C,kHAAA,eAAe,OAAA;AACxB,qDAAmD;AAA1C,kHAAA,eAAe,OAAA;AACxB,gDAA8C;AAArC,0GAAA,WAAW,OAAA;AACpB,wDAAsD;AAA7C,kHAAA,eAAe,OAAA;AACxB,2DAAyD;AAAhD,wHAAA,kBAAkB,OAAA;AAI3B,uCAA6C;AAApC,4GAAA,gBAAgB,OAAA;AAMzB,iDAA+C;AAAtC,8GAAA,aAAa,OAAA;AACtB,yCAAiD;AAAxC,qGAAA,QAAQ,OAAA;AAAE,sGAAA,SAAS,OAAA", "sourcesContent": ["import { Arch } from \"builder-util\"\nimport { CancellationToken } from \"builder-util-runtime\"\nimport { MultiProgress } from \"./multiProgress\"\n\nexport { BitbucketPublisher } from \"./bitbucketPublisher\"\nexport { GitHubPublisher } from \"./gitHubPublisher\"\nexport { KeygenPublisher } from \"./keygenPublisher\"\nexport { S3Publisher } from \"./s3/s3Publisher\"\nexport { SpacesPublisher } from \"./s3/spacesPublisher\"\nexport { SnapStorePublisher } from \"./snapStorePublisher\"\n\nexport type PublishPolicy = \"onTag\" | \"onTagOrDraft\" | \"always\" | \"never\"\n\nexport { ProgressCallback } from \"./progress\"\n\nexport interface PublishOptions {\n  publish?: PublishPolicy | null\n}\n\nexport { HttpPublisher } from \"./httpPublisher\"\nexport { getCiTag, Publisher } from \"./publisher\"\n\nexport interface PublishContext {\n  readonly cancellationToken: CancellationToken\n  readonly progress: MultiProgress | null\n}\n\nexport interface UploadTask {\n  file: string\n  fileContent?: Buffer | null\n\n  arch: Arch | null\n  safeArtifactName?: string | null\n  timeout?: number | null\n}\n"]}