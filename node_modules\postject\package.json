{"name": "postject", "version": "1.0.0-alpha.6", "description": "Easily inject arbitrary read-only resources into executable formats (Mach-O, PE, ELF) and use it at runtime.", "license": "MIT", "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "**************:nodejs/postject.git"}, "homepage": "https://github.com/nodejs/postject#readme", "bin": {"postject": "./dist/cli.js"}, "main": "dist/api.js", "scripts": {"build": "zx ./scripts/build.mjs", "clean": "rimraf ./build", "format": "npm run format:cpp && npm run format:js", "format:cpp": "clang-format -style=chromium -i postject-api.h src/**.cpp test/**.c test/**.cpp", "format:js": "prettier --write src/**.js scripts/**.mjs test/**.mjs", "lint": "npm run lint:cpp && npm run lint:js", "lint:cpp": "clang-format -style=chromium --dry-run --Werror postject-api.h src/**.cpp test/**.c test/**.cpp", "lint:js": "prettier --check src/**.js scripts/**.mjs test/**.mjs", "test": "mocha"}, "dependencies": {"commander": "^9.4.0"}, "devDependencies": {"chai": "^4.3.6", "chai-as-promised": "^7.1.1", "clang-format": "^1.8.0", "esbuild": "^0.15.10", "fs-extra": "^10.1.0", "mocha": "^10.0.0", "mocha-junit-reporter": "^2.0.2", "prettier": "^2.7.1", "rimraf": "^3.0.2", "tempy": "^3.0.0", "zx": "^7.1.1"}}