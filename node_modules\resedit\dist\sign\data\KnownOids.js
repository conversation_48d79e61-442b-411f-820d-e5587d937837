"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OID_RFC3161_COUNTER_SIGNATURE = exports.OID_SPC_INDIVIDUAL_SP_KEY_PURPOSE_OBJID = exports.OID_SPC_SP_OPUS_INFO_OBJID = exports.OID_SPC_STATEMENT_TYPE_OBJID = exports.OID_MESSAGE_DIGEST = exports.OID_CONTENT_TYPE = exports.OID_SIGNED_DATA = exports.OID_DSA = exports.OID_RSA = exports.OID_SHAKE256_NO_SIGN = exports.OID_SHAKE128_NO_SIGN = exports.OID_SHA3_512_NO_SIGN = exports.OID_SHA3_384_NO_SIGN = exports.OID_SHA3_256_NO_SIGN = exports.OID_SHA3_224_NO_SIGN = exports.OID_SHA512_256_NO_SIGN = exports.OID_SHA512_224_NO_SIGN = exports.OID_SHA224_NO_SIGN = exports.OID_SHA512_NO_SIGN = exports.OID_SHA384_NO_SIGN = exports.OID_SHA256_NO_SIGN = exports.OID_SHA1_NO_SIGN = void 0;
var ObjectIdentifier_js_1 = require("./ObjectIdentifier.js");
// 1.3.14.3.2.26
// prettier-ignore
exports.OID_SHA1_NO_SIGN = new ObjectIdentifier_js_1.default([1, 3, 14, 3, 2, 26]);
// 2.16.840.1.101.3.4.2.1
// prettier-ignore
exports.OID_SHA256_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 1]);
// 2.16.840.1.101.3.4.2.2
// prettier-ignore
exports.OID_SHA384_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 2]);
// 2.16.840.1.101.3.4.2.3
// prettier-ignore
exports.OID_SHA512_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 3]);
// 2.16.840.1.101.3.4.2.4
// prettier-ignore
exports.OID_SHA224_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 4]);
// 2.16.840.1.101.3.4.2.5
// prettier-ignore
exports.OID_SHA512_224_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 5]);
// 2.16.840.1.101.3.4.2.6
// prettier-ignore
exports.OID_SHA512_256_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 6]);
// 2.16.840.1.101.3.4.2.7
// prettier-ignore
exports.OID_SHA3_224_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 7]);
// 2.16.840.1.101.3.4.2.8
// prettier-ignore
exports.OID_SHA3_256_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 8]);
// 2.16.840.1.101.3.4.2.9
// prettier-ignore
exports.OID_SHA3_384_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 9]);
// 2.16.840.1.101.3.4.2.10
// prettier-ignore
exports.OID_SHA3_512_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 10]);
// 2.16.840.1.101.3.4.2.11
// prettier-ignore
exports.OID_SHAKE128_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 11]);
// 2.16.840.1.101.3.4.2.12
// prettier-ignore
exports.OID_SHAKE256_NO_SIGN = new ObjectIdentifier_js_1.default([2, 16, 840, 1, 101, 3, 4, 2, 12]);
// 1.2.840.113549.1.1.1
// prettier-ignore
exports.OID_RSA = new ObjectIdentifier_js_1.default([1, 2, 840, 113549, 1, 1, 1]);
// 1.2.840.10040.4.1
// prettier-ignore
exports.OID_DSA = new ObjectIdentifier_js_1.default([1, 2, 840, 10040, 4, 1]);
// 1.2.840.113549.1.7.2
// prettier-ignore
exports.OID_SIGNED_DATA = new ObjectIdentifier_js_1.default([1, 2, 840, 113549, 1, 7, 2]);
// 1.2.840.113549.1.9.3
// prettier-ignore
exports.OID_CONTENT_TYPE = new ObjectIdentifier_js_1.default([1, 2, 840, 113549, 1, 9, 3]);
// 1.2.840.113549.1.9.4
// prettier-ignore
exports.OID_MESSAGE_DIGEST = new ObjectIdentifier_js_1.default([1, 2, 840, 113549, 1, 9, 4]);
// 1.3.6.1.4.1.311.2.1.11
// prettier-ignore
exports.OID_SPC_STATEMENT_TYPE_OBJID = new ObjectIdentifier_js_1.default([1, 3, 6, 1, 4, 1, 311, 2, 1, 11]);
// 1.3.6.1.4.1.311.2.1.12
// prettier-ignore
exports.OID_SPC_SP_OPUS_INFO_OBJID = new ObjectIdentifier_js_1.default([1, 3, 6, 1, 4, 1, 311, 2, 1, 12]);
// 1.3.6.1.4.1.311.2.1.21
// prettier-ignore
exports.OID_SPC_INDIVIDUAL_SP_KEY_PURPOSE_OBJID = new ObjectIdentifier_js_1.default([1, 3, 6, 1, 4, 1, 311, 2, 1, 21]);
// 1.3.6.1.4.1.311.3.3.1
// prettier-ignore
exports.OID_RFC3161_COUNTER_SIGNATURE = new ObjectIdentifier_js_1.default([1, 3, 6, 1, 4, 1, 311, 3, 3, 1]);
