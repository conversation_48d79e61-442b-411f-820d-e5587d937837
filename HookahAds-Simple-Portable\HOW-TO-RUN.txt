HOOKAH ADS - SIMPLE PORTABLE VERSION

FIXED: Text now scrolls across the FULL SCREEN from right to left!
NEW: Added 5 font size options (50% to 90% of banner height)!
NEW: Stable Alignment - All elements properly aligned regardless of banner height!
 
REQUIREMENTS: 
- Node.js must be installed on the target computer 
- Download from: https://nodejs.org/ 
 
TO RUN THE APPLICATION: 
1. Double-click "START-HOOKAH-ADS.bat" 
 
FEATURES: 
- Banner window appears at bottom of screen 
- Control panel opens for managing ads 
- Add text, visual elements, AND pictures to your advertisements
- Customize colors, height, font size, and scroll speed
- Choose from 5 font sizes: Small, Medium-Small, Medium, Medium-Large, Large
- ALL ELEMENTS properly aligned regardless of banner height changes
- Pictures sized at 80% of font size for better alignment with text
- Text scrolls across the FULL SCREEN width from right to left
- NEW: Picture Box system with 3 types of elements:
  * Emoji/symbols (15+ options): 🔥⭐🎉💎🌟🏆❤️🎯⚡🎪🌙☀️🎵🍃💫
  * Custom decorative text: ★★★ PREMIUM ★★★, ═══ SPECIAL ═══, 🔥🔥🔥
  * Picture Box: Upload photos that display in a styled box after text
- Sequence: Text → Emoji → Custom Text → Picture Box → Text → Emoji → Custom Text → Picture Box
- Smooth continuous scrolling with proper looping
 
This version includes all source files and dependencies.
Just copy this folder to any computer with Node.js installed.

IMAGE TIPS:
- Use images from Desktop or Documents folder (avoid network drives)
- Keep file names simple (no special characters or spaces)
- Supported formats: JPG, PNG, GIF, BMP
- Keep images under 5MB each for best performance

PICTURE BOX SYSTEM:
- 3 types of visual elements you can use:
  1. EMOJI/SYMBOLS: Choose from dropdown (🔥 Fire, ⭐ Star, 🎉 Party, etc.)
  2. CUSTOM TEXT: Add decorative text (★★★ PREMIUM ★★★, ═══ SPECIAL ═══)
  3. PICTURE DISPLAY: Upload photos that display pure (90% of banner height) without any styling
- Sequence: Text → Emoji → Custom Text → Picture Box → repeat
- Picture box has professional styling with borders, shadows, and backgrounds
- Uses base64 conversion for reliable picture display
- Preview your uploaded picture in the control panel before applying
