HOOKAH ADS - SIMPL<PERSON> PORTABLE VERSION

FIXED: Text now scrolls across the FULL SCREEN from right to left!
NEW: Added 5 font size options (50% to 90% of banner height)!
NEW: Visual Elements System - Text first, then emoji/symbols, then repeat!
 
REQUIREMENTS: 
- Node.js must be installed on the target computer 
- Download from: https://nodejs.org/ 
 
TO RUN THE APPLICATION: 
1. Double-click "START-HOOKAH-ADS.bat" 
 
FEATURES: 
- Banner window appears at bottom of screen 
- Control panel opens for managing ads 
- Add text and visual elements to your advertisements
- Customize colors, height, font size, and scroll speed
- Choose from 5 font sizes: Small, Medium-Small, Medium, Medium-Large, Large
- Text scrolls across the FULL SCREEN width from right to left
- NEW: Visual Elements - Choose emoji/symbols or add custom decorative text
- Sequence format - Text appears first, then visual elements, then repeats
- Smooth continuous scrolling with proper looping
- 15+ emoji options: 🔥⭐🎉💎🌟🏆❤️🎯⚡🎪🌙☀️🎵🍃💫
 
This version includes all source files and dependencies.
Just copy this folder to any computer with Node.js installed.

IMAGE TIPS:
- Use images from Desktop or Documents folder (avoid network drives)
- Keep file names simple (no special characters or spaces)
- Supported formats: JPG, PNG, GIF, BMP
- Keep images under 5MB each for best performance

NEW VISUAL ELEMENTS SYSTEM:
- Instead of image files, use emoji and symbols for visual impact
- Choose from dropdown: 🔥 Fire, ⭐ Star, 🎉 Party, 💎 Diamond, etc.
- Add custom decorative text: ★★★ PREMIUM ★★★, ═══ SPECIAL ═══
- Visual elements appear after text in the scrolling sequence
- No file loading issues - emoji and symbols always work!
- Text should appear twice, then visual elements, then repeat
