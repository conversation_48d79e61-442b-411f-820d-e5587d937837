HOOKAH ADS - SIMPL<PERSON> PORTABLE VERSION

FIXED: Text now scrolls across the FULL SCREEN from right to left!
NEW: Added 5 font size options (50% to 90% of banner height)!
NEW: File-Based Templates - 3 default templates + reliable file saving system!
 
REQUIREMENTS: 
- Node.js must be installed on the target computer 
- Download from: https://nodejs.org/ 
 
TO RUN THE APPLICATION: 
1. Double-click "START-HOOKAH-ADS.bat" 
 
FEATURES: 
- Banner window appears at bottom of screen 
- Control panel opens for managing ads 
- Add text, visual elements, AND pictures to your advertisements
- Customize colors, height, font size, and scroll speed
- Choose from 5 font sizes: Small, Medium-Small, Medium, Medium-Large, Large
- NEW: Slower scroll speeds available: 15s (Fast) to 120s (Very Slow)
- ALL ELEMENTS properly aligned regardless of banner height changes
- Pictures sized at 80% of font size for better alignment with text
- Text scrolls across the FULL SCREEN width from right to left
- NEW: Template System with 3 default templates:
  * Template 1: Store Hours & Info (2 sequences)
  * Template 2: Special Offers (3 sequences)
  * Template 3: VIP & Premium Services (2 sequences)
  * Load Template: Instantly fills all fields with saved settings
  * Save Template: Save your current configuration for later use
- No more filling fields each time - just click "Load Template" and go!
- Smooth continuous scrolling with proper looping
 
This version includes all source files and dependencies.
Just copy this folder to any computer with Node.js installed.

IMAGE TIPS:
- Use images from Desktop or Documents folder (avoid network drives)
- Keep file names simple (no special characters or spaces)
- Supported formats: JPG, PNG, GIF, BMP
- Keep images under 5MB each for best performance

QUICK START WITH TEMPLATES:
1. Double-click "START-HOOKAH-ADS.bat"
2. Two windows open: Banner (bottom of screen) + Control Panel
3. EASY WAY: Click "📋 Load Template 1" (or 2, or 3) for instant setup
4. OR: Enter your own text, choose colors, adjust settings
5. Click "Apply Changes" to see your banner
6. Banner scrolls continuously across the bottom of your screen

TEMPLATE SYSTEM:
- 📋 Load Template: Instantly loads pre-configured advertisements
- 💾 Save Template: Save your current settings to file for future use
- Template 1: Store hours and general info (2 sequences)
- Template 2: Special offers and promotions (3 sequences)
- Template 3: VIP services and premium features (2 sequences)
- Templates save to: /templates/saved-templates.json file
- Reliable file-based storage - no browser limitations
- No more filling fields each time - just click and go!

MULTIPLE SEQUENCES SYSTEM:
- Up to 3 different advertisement sequences
- Each sequence: Text + Emoji + Custom Text + Picture
- Automatic cycling: Sequence1 → Sequence2 → Sequence3 → repeat
- Color-coded sections: Green (Seq1), Blue (Seq2), Orange (Seq3)
- Example: "Store Hours" → "Special Offers" → "VIP Services" → repeat
