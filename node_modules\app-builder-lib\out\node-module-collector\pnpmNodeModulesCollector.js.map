{"version": 3, "file": "pnpmNodeModulesCollector.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/pnpmNodeModulesCollector.ts"], "names": [], "mappings": ";;;;AAAA,uCAA+B;AAC/B,iEAA6D;AAE7D,6BAA4B;AAC5B,+CAAwC;AAExC,MAAa,wBAAyB,SAAQ,2CAAoD;IAChG,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAA;QAeG,cAAS,GAAiB,EAAwB,CAAC,SAAS,CAAA;QAC/D,mBAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,mBAAmB,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAA;IAfhJ,CAAC;IAiBS,OAAO;QACf,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;IAC5D,CAAC;IAES,mBAAmB,CAAC,OAAuB;QACnD,MAAM,IAAI,GAAG,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAA;QAC/C,OAAO;YACL,GAAG,IAAI;YACP,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,oBAAoB,CAAC;SACzE,CAAA;IACH,CAAC;IAED,+BAA+B,CAAC,IAAoB;QAClD,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACrD,MAAM,WAAW,GAA+B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAA;QAErF,MAAM,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,oBAAoB,IAAI,EAAE,CAAC,EAAE,CAAA;QACnF,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAiC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;;YAC7F,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,GAAG,IAAI,CAAA;YAEtC,IAAI,UAAmB,CAAA;YACvB,IAAI,MAAA,WAAW,CAAC,YAAY,0CAAG,WAAW,CAAC,EAAE,CAAC;gBAC5C,UAAU,GAAG,KAAK,CAAA;YACpB,CAAC;iBAAM,IAAI,MAAA,WAAW,CAAC,oBAAoB,0CAAG,WAAW,CAAC,EAAE,CAAC;gBAC3D,UAAU,GAAG,IAAI,CAAA;YACnB,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,CAAA;YACZ,CAAC;YAED,IAAI,CAAC;gBACH,OAAO;oBACL,GAAG,GAAG;oBACN,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,+BAA+B,CAAC,UAAU,CAAC;iBAChE,CAAA;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,UAAU,EAAE,CAAC;oBACf,OAAO,GAAG,CAAA;gBACZ,CAAC;gBACD,MAAM,KAAK,CAAA;YACb,CAAC;QACH,CAAC,EAAE,EAAE,CAAC,CAAA;QAEN,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAA;QAC7D,MAAM,OAAO,GAAmB;YAC9B,IAAI;YACJ,OAAO;YACP,IAAI,EAAE,WAAW;YACjB,UAAU;YACV,YAAY;YACZ,4BAA4B,EAAE,KAAK;SACpC,CAAA;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAES,qBAAqB,CAAC,QAAgB;QAC9C,MAAM,cAAc,GAAqB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC7D,4CAA4C;QAC5C,OAAO,cAAc,CAAC,CAAC,CAAC,CAAA;IAC1B,CAAC;;AA9EH,4DA+EC;;AA1EiB,kCAAS,GAAG,IAAI,eAAI,CAAS,KAAK,IAAI,EAAE;IACtD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC;YACH,MAAM,IAAA,mBAAI,EAAC,MAAM,EAAE,CAAC,WAAW,CAAC,CAAC,CAAA;QACnC,CAAC;QAAC,OAAO,MAAW,EAAE,CAAC;YACrB,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,6CAA6C,CAAC,CAAA;YAC9D,OAAO,UAAU,CAAA;QACnB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC,CAAC,AAVuB,CAUvB", "sourcesContent": ["import { <PERSON><PERSON> } from \"lazy-val\"\nimport { NodeModulesCollector } from \"./nodeModulesCollector\"\nimport { Dependency, DependencyTree, PnpmDependency } from \"./types\"\nimport * as path from \"path\"\nimport { exec, log } from \"builder-util\"\n\nexport class PnpmNodeModulesCollector extends NodeModulesCollector<PnpmDependency, PnpmDependency> {\n  constructor(rootDir: string) {\n    super(rootDir)\n  }\n\n  static readonly pmCommand = new Lazy<string>(async () => {\n    if (process.platform === \"win32\") {\n      try {\n        await exec(\"pnpm\", [\"--version\"])\n      } catch (_error: any) {\n        log.debug(null, \"pnpm not detected, falling back to pnpm.cmd\")\n        return \"pnpm.cmd\"\n      }\n    }\n    return \"pnpm\"\n  })\n\n  protected readonly pmCommand: Lazy<string> = PnpmNodeModulesCollector.pmCommand\n  public readonly installOptions = this.pmCommand.value.then(cmd => ({ cmd, args: [\"install\", \"--frozen-lockfile\"], lockfile: \"pnpm-lock.yaml\" }))\n\n  protected getArgs(): string[] {\n    return [\"list\", \"--prod\", \"--json\", \"--depth\", \"Infinity\"]\n  }\n\n  protected extractRelevantData(npmTree: PnpmDependency): PnpmDependency {\n    const tree = super.extractRelevantData(npmTree)\n    return {\n      ...tree,\n      optionalDependencies: this.extractInternal(npmTree.optionalDependencies),\n    }\n  }\n\n  extractProductionDependencyTree(tree: PnpmDependency): DependencyTree {\n    const p = path.normalize(this.resolvePath(tree.path))\n    const packageJson: Dependency<string, string> = require(path.join(p, \"package.json\"))\n\n    const deps = { ...(tree.dependencies || {}), ...(tree.optionalDependencies || {}) }\n    const dependencies = Object.entries(deps).reduce<DependencyTree[\"dependencies\"]>((acc, curr) => {\n      const [packageName, dependency] = curr\n\n      let isOptional: boolean\n      if (packageJson.dependencies?.[packageName]) {\n        isOptional = false\n      } else if (packageJson.optionalDependencies?.[packageName]) {\n        isOptional = true\n      } else {\n        return acc\n      }\n\n      try {\n        return {\n          ...acc,\n          [packageName]: this.extractProductionDependencyTree(dependency),\n        }\n      } catch (error) {\n        if (isOptional) {\n          return acc\n        }\n        throw error\n      }\n    }, {})\n\n    const { name, version, path: packagePath, workspaces } = tree\n    const depTree: DependencyTree = {\n      name,\n      version,\n      path: packagePath,\n      workspaces,\n      dependencies,\n      implicitDependenciesInjected: false,\n    }\n    return depTree\n  }\n\n  protected parseDependenciesTree(jsonBlob: string): PnpmDependency {\n    const dependencyTree: PnpmDependency[] = JSON.parse(jsonBlob)\n    // pnpm returns an array of dependency trees\n    return dependencyTree[0]\n  }\n}\n"]}