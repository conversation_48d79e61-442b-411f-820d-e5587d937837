{"version": 3, "file": "updateInfoBuilder.js", "sourceRoot": "", "sources": ["../../src/publish/updateInfoBuilder.ts"], "names": [], "mappings": ";;AAmGA,sDAmEC;AA0BD,oDA+CC;AA/OD,qDAAuC;AACvC,+CAA4E;AAE5E,uCAA2D;AAC3D,uCAA+B;AAC/B,6BAA4B;AAC5B,iCAAgC;AAChC,kCAAkC;AAKlC,uCAAuC;AACvC,qDAAqF;AAErF,KAAK,UAAU,cAAc,CAAC,QAA+B;IAC3D,MAAM,WAAW,GAAgB,EAAE,GAAG,CAAC,QAAQ,CAAC,4BAA4B,CAAC,WAAW,IAAI,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAA;IAC1H,IAAI,WAAW,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;QACrC,MAAM,gBAAgB,GAAG,MAAM,QAAQ,CAAC,WAAW,CACjD,WAAW,CAAC,gBAAgB,EAC5B,iBAAiB,QAAQ,CAAC,QAAQ,CAAC,qBAAqB,KAAK,EAC7D,iBAAiB,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,EAC5C,iBAAiB,QAAQ,CAAC,QAAQ,CAAC,QAAQ,KAAK,EAChD,kBAAkB,CACnB,CAAA;QACD,MAAM,YAAY,GAAG,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAA,mBAAQ,EAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;QAChG,iDAAiD;QACjD,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,WAAW,CAAC,YAAY,GAAG,YAAY,CAAA;QACzC,CAAC;IACH,CAAC;IACD,OAAO,WAAW,CAAC,gBAAgB,CAAA;IACnC,OAAO,WAAW,CAAA;AACpB,CAAC;AAED,SAAS,oCAAoC,CAAC,QAA+B;IAC3E,MAAM,KAAK,GAAG,QAAQ,CAAC,4BAA4B,CAAC,kCAAkC,CAAA;IACtF,OAAO,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC,CAAC,KAAK,CAAA;AACnF,CAAC;AAED;;;;GAIG;AACH,SAAS,mBAAmB,CAAC,QAA+B,EAAE,aAAmC;IAC/F,MAAM,cAAc,GAAY,aAAsC,CAAC,OAAO,IAAI,QAAQ,CAAA;IAC1F,+CAA+C;IAC/C,IAAI,cAAc,KAAK,OAAO,IAAI,aAAa,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,oCAAoC,CAAC,QAAQ,CAAC,EAAE,CAAC;QACzH,OAAO,CAAC,cAAc,CAAC,CAAA;IACzB,CAAC;IAED,QAAQ,cAAc,EAAE,CAAC;QACvB,KAAK,MAAM;YACT,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAElC,KAAK,QAAQ;YACX,OAAO,CAAC,cAAc,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;QAE1C;YACE,OAAO,CAAC,cAAc,CAAC,CAAA;IAC3B,CAAC;AACH,CAAC;AAED,SAAS,qBAAqB,CAAC,OAAe,EAAE,QAA+B,EAAE,IAAiB;IAChG,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,KAAK,eAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAA;IAC5G,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,0BAA0B,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAA;AACjF,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAiB,EAAE,QAA+B;IACpF,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,mBAAI,CAAC,GAAG,IAAI,QAAQ,CAAC,QAAQ,KAAK,eAAQ,CAAC,KAAK,EAAE,CAAC;QAC9E,OAAO,EAAE,CAAA;IACX,CAAC;IACD,OAAO,IAAI,KAAK,mBAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,mBAAI,CAAC,IAAI,CAAC,EAAE,CAAA;AACzD,CAAC;AAUD,SAAS,yCAAyC,CAAC,oBAAmC,EAAE,oBAA0C,EAAE,QAAkB;IACpJ,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAA;IACxD,CAAC;IAED,2EAA2E;IAC3E,IAAI,oBAAoB,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAC/C,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAA;IACzH,OAAO,cAAc,IAAI,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;AACrE,CAAC;AAED,gBAAgB;AACT,KAAK,UAAU,qBAAqB,CAAC,KAAsB,EAAE,eAA4C;IAC9G,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAA;IAC/B,MAAM,cAAc,GAAG,MAAM,IAAA,+CAA8B,EAAC,QAAQ,EAAE,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;IAClG,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1D,OAAO,EAAE,CAAA;IACX,CAAC;IAED,MAAM,MAAM,GAAG,KAAK,CAAC,MAAO,CAAC,MAAM,CAAA;IACnC,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAA;IACxC,MAAM,IAAI,GAAG,IAAI,eAAI,CAAS,GAAG,EAAE,CAAC,IAAA,eAAQ,EAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAA;IAC1E,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,KAAK,eAAQ,CAAC,GAAG,CAAA;IAChD,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAA;IACtC,MAAM,UAAU,GAAG,MAAM,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA;IACzF,MAAM,KAAK,GAA8B,EAAE,CAAA;IAC3C,MAAM,4BAA4B,GAAG,QAAQ,CAAC,4BAA4B,CAAC,4BAA4B,IAAI,QAAQ,CAAC,MAAM,CAAC,4BAA4B,IAAI,QAAQ,CAAA;IACnK,KAAK,MAAM,oBAAoB,IAAI,cAAc,EAAE,CAAC;QAClD,IAAI,GAAG,GAAG,MAAM,CAAA;QAChB,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,oBAAoB,KAAK,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5E,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAA;QACxD,CAAC;QAED,IAAI,gCAAgC,GAAG,yCAAyC,CAAC,4BAA4B,EAAE,oBAAoB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;QAEnJ,IAAI,IAAI,GAAG,UAAU,CAAA;QACrB,mCAAmC;QACnC,IAAI,gCAAgC,IAAI,QAAQ,CAAC,QAAQ,KAAK,eAAQ,CAAC,OAAO,EAAE,CAAC;YAC/E,IAAI,GAAG;gBACL,GAAG,IAAI;aACR,CAEA;YAAC,IAA0B,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAA;QACtD,CAAC;QAED,IAAI,KAAK,CAAC,gBAAgB,IAAI,IAAI,IAAI,oBAAoB,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;YACnC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,gBAAgB,CAAA;YACxC,IAAI,GAAG;gBACL,GAAG,IAAI;gBACP,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,KAAK,CAAC,gBAAgB;aAC7B,CAAA;QACH,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,mBAAmB,CAAC,QAAQ,EAAE,oBAAoB,CAAC,EAAE,CAAC;YAC1E,IAAI,KAAK,IAAI,gCAAgC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7E,yIAAyI;gBACzI,gCAAgC,GAAG,KAAK,CAAA;gBACxC,MAAM,eAAe,CAAC,oBAAoB,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;YACpG,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,qBAAqB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;YAC3F,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;gBACrC,SAAQ;YACV,CAAC;YAED,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;YAEhC,kEAAkE;YAClE,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,cAAc;gBACpB,IAAI;gBACJ,oBAAoB;gBACpB,QAAQ;aACT,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,KAAK,UAAU,gBAAgB,CAAC,OAAe,EAAE,KAAsB,EAAE,WAAwB;IAC/F,MAAM,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAA;IACzC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACrC,MAAM,MAAM,GAAG,CAAC,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,IAAA,eAAQ,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC1G,MAAM,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAA;IAC/B,MAAM,MAAM,GAAe;QACzB,aAAa;QACb,OAAO;QACP,aAAa;QACb,KAAK;QACL,aAAa;QACb,IAAI,EAAE,GAAG,CAAC,4EAA4E;QACtF,aAAa;QACb,MAAM,CAAC,4EAA4E;QACnF,GAAI,WAA0B;KAC/B,CAAA;IAED,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,gDAAgD;QAChD,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;IACnF,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAEM,KAAK,UAAU,oBAAoB,CAAC,mBAA8C,EAAE,QAAkB;IAC3G,gGAAgG;IAChG,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;IAEvI,MAAM,uBAAuB,GAAG,IAAI,GAAG,EAA8B,CAAA;IACrE,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;QACvC,kEAAkE;QAClE,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,IAAA,gCAAiB,EAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAA;QACpG,MAAM,YAAY,GAAG,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACrD,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YACtC,SAAQ;QACV,CAAC;QAED,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAClD,CAAC;IAED,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;IAC5C,MAAM,WAAW,GAAG,CAAC,CAAA;IACrB,MAAM,IAAA,yBAAS,EAA2B,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAC,IAAI,EAAC,EAAE;QAChH,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAA;QAC/C,IAAI,aAAa,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAC9C,kBAAG,CAAC,KAAK,CACP;gBACE,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,mCAAmC;aAC5C,EACD,yCAAyC,CAC1C,CAAA;YACD,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QACrC,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,8BAAe,EAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;QACxE,MAAM,IAAA,qBAAU,EAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA;QACxC,MAAM,QAAQ,CAAC,mBAAmB,CAAC;YACjC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW;YACX,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI;YACZ,aAAa;SACd,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,2CAA2C;AAC3C,KAAK,UAAU,eAAe,CAC5B,aAAmC,EACnC,MAAc,EACd,GAAW,EACX,OAAe,EACf,YAAyB,EACzB,OAAe,EACf,QAA+B;IAE/B,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,KAAK,QAAQ,CAAA;IACpD,MAAM,cAAc,GAAG,QAAQ,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,WAAW,CAAC,CAAA;IAC3I,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;QACtC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAChC,MAAM,IAAA,qBAAU,EACd,cAAc,EACd;YACE,OAAO;YACP,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACrC,GAAG,EAAE,IAAA,mCAAkB,EAAC,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC;SACjG,EACD,EAAE,MAAM,EAAE,CAAC,EAAE,CACd,CAAA;QAED,MAAM,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC;YACtC,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,IAAI;YACV,QAAQ;YACR,MAAM,EAAE,IAAI;YACZ,aAAa;SACd,CAAC,CAAA;IACJ,CAAC;AACH,CAAC", "sourcesContent": ["import asyncPool from \"tiny-async-pool\"\nimport { Arch, log, safeStringify<PERSON>son, serializeToYaml } from \"builder-util\"\nimport { GenericServerOptions, PublishConfiguration, UpdateInfo, WindowsUpdateInfo } from \"builder-util-runtime\"\nimport { outputFile, outputJson, readFile } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport * as semver from \"semver\"\nimport { Platform } from \"../core\"\nimport { ReleaseInfo } from \"../options/PlatformSpecificBuildOptions\"\nimport { Packager } from \"../packager\"\nimport { ArtifactCreated } from \"../packagerApi\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { hashFile } from \"../util/hash\"\nimport { computeDownloadUrl, getPublishConfigsForUpdateInfo } from \"./PublishManager\"\n\nasync function getReleaseInfo(packager: PlatformPackager<any>) {\n  const releaseInfo: ReleaseInfo = { ...(packager.platformSpecificBuildOptions.releaseInfo || packager.config.releaseInfo) }\n  if (releaseInfo.releaseNotes == null) {\n    const releaseNotesFile = await packager.getResource(\n      releaseInfo.releaseNotesFile,\n      `release-notes-${packager.platform.buildConfigurationKey}.md`,\n      `release-notes-${packager.platform.name}.md`,\n      `release-notes-${packager.platform.nodeName}.md`,\n      \"release-notes.md\"\n    )\n    const releaseNotes = releaseNotesFile == null ? null : await readFile(releaseNotesFile, \"utf-8\")\n    // to avoid undefined in the file, check for null\n    if (releaseNotes != null) {\n      releaseInfo.releaseNotes = releaseNotes\n    }\n  }\n  delete releaseInfo.releaseNotesFile\n  return releaseInfo\n}\n\nfunction isGenerateUpdatesFilesForAllChannels(packager: PlatformPackager<any>) {\n  const value = packager.platformSpecificBuildOptions.generateUpdatesFilesForAllChannels\n  return value == null ? packager.config.generateUpdatesFilesForAllChannels : value\n}\n\n/**\n if this is an \"alpha\" version, we need to generate only the \"alpha\" .yml file\n if this is a \"beta\" version, we need to generate both the \"alpha\" and \"beta\" .yml file\n if this is a \"stable\" version, we need to generate all the \"alpha\", \"beta\" and \"stable\" .yml file\n */\nfunction computeChannelNames(packager: PlatformPackager<any>, publishConfig: PublishConfiguration): Array<string> {\n  const currentChannel: string = (publishConfig as GenericServerOptions).channel || \"latest\"\n  // for GitHub should be pre-release way be used\n  if (currentChannel === \"alpha\" || publishConfig.provider === \"github\" || !isGenerateUpdatesFilesForAllChannels(packager)) {\n    return [currentChannel]\n  }\n\n  switch (currentChannel) {\n    case \"beta\":\n      return [currentChannel, \"alpha\"]\n\n    case \"latest\":\n      return [currentChannel, \"alpha\", \"beta\"]\n\n    default:\n      return [currentChannel]\n  }\n}\n\nfunction getUpdateInfoFileName(channel: string, packager: PlatformPackager<any>, arch: Arch | null): string {\n  const osSuffix = packager.platform === Platform.WINDOWS ? \"\" : `-${packager.platform.buildConfigurationKey}`\n  return `${channel}${osSuffix}${getArchPrefixForUpdateFile(arch, packager)}.yml`\n}\n\nfunction getArchPrefixForUpdateFile(arch: Arch | null, packager: PlatformPackager<any>) {\n  if (arch == null || arch === Arch.x64 || packager.platform !== Platform.LINUX) {\n    return \"\"\n  }\n  return arch === Arch.armv7l ? \"-arm\" : `-${Arch[arch]}`\n}\n\nexport interface UpdateInfoFileTask {\n  readonly file: string\n  readonly info: UpdateInfo\n  readonly publishConfiguration: PublishConfiguration\n\n  readonly packager: PlatformPackager<any>\n}\n\nfunction computeIsisElectronUpdater1xCompatibility(updaterCompatibility: string | null, publishConfiguration: PublishConfiguration, packager: Packager) {\n  if (updaterCompatibility != null) {\n    return semver.satisfies(\"1.0.0\", updaterCompatibility)\n  }\n\n  // spaces is a new publish provider, no need to keep backward compatibility\n  if (publishConfiguration.provider === \"spaces\") {\n    return false\n  }\n\n  const updaterVersion = packager.metadata.dependencies == null ? null : packager.metadata.dependencies[\"electron-updater\"]\n  return updaterVersion == null || semver.lt(updaterVersion, \"4.0.0\")\n}\n\n/** @internal */\nexport async function createUpdateInfoTasks(event: ArtifactCreated, _publishConfigs: Array<PublishConfiguration>): Promise<Array<UpdateInfoFileTask>> {\n  const packager = event.packager\n  const publishConfigs = await getPublishConfigsForUpdateInfo(packager, _publishConfigs, event.arch)\n  if (publishConfigs == null || publishConfigs.length === 0) {\n    return []\n  }\n\n  const outDir = event.target!.outDir\n  const version = packager.appInfo.version\n  const sha2 = new Lazy<string>(() => hashFile(event.file, \"sha256\", \"hex\"))\n  const isMac = packager.platform === Platform.MAC\n  const createdFiles = new Set<string>()\n  const sharedInfo = await createUpdateInfo(version, event, await getReleaseInfo(packager))\n  const tasks: Array<UpdateInfoFileTask> = []\n  const electronUpdaterCompatibility = packager.platformSpecificBuildOptions.electronUpdaterCompatibility || packager.config.electronUpdaterCompatibility || \">=2.15\"\n  for (const publishConfiguration of publishConfigs) {\n    let dir = outDir\n    if (publishConfigs.length > 1 && publishConfiguration !== publishConfigs[0]) {\n      dir = path.join(outDir, publishConfiguration.provider)\n    }\n\n    let isElectronUpdater1xCompatibility = computeIsisElectronUpdater1xCompatibility(electronUpdaterCompatibility, publishConfiguration, packager.info)\n\n    let info = sharedInfo\n    // noinspection JSDeprecatedSymbols\n    if (isElectronUpdater1xCompatibility && packager.platform === Platform.WINDOWS) {\n      info = {\n        ...info,\n      }\n      // noinspection JSDeprecatedSymbols\n      ;(info as WindowsUpdateInfo).sha2 = await sha2.value\n    }\n\n    if (event.safeArtifactName != null && publishConfiguration.provider === \"github\") {\n      const newFiles = info.files.slice()\n      newFiles[0].url = event.safeArtifactName\n      info = {\n        ...info,\n        files: newFiles,\n        path: event.safeArtifactName,\n      }\n    }\n\n    for (const channel of computeChannelNames(packager, publishConfiguration)) {\n      if (isMac && isElectronUpdater1xCompatibility && event.file.endsWith(\".zip\")) {\n        // write only for first channel (generateUpdatesFilesForAllChannels is a new functionality, no need to generate old mac update info file)\n        isElectronUpdater1xCompatibility = false\n        await writeOldMacInfo(publishConfiguration, outDir, dir, channel, createdFiles, version, packager)\n      }\n\n      const updateInfoFile = path.join(dir, getUpdateInfoFileName(channel, packager, event.arch))\n      if (createdFiles.has(updateInfoFile)) {\n        continue\n      }\n\n      createdFiles.add(updateInfoFile)\n\n      // artifact should be uploaded only to designated publish provider\n      tasks.push({\n        file: updateInfoFile,\n        info,\n        publishConfiguration,\n        packager,\n      })\n    }\n  }\n  return tasks\n}\n\nasync function createUpdateInfo(version: string, event: ArtifactCreated, releaseInfo: ReleaseInfo): Promise<UpdateInfo> {\n  const customUpdateInfo = event.updateInfo\n  const url = path.basename(event.file)\n  const sha512 = (customUpdateInfo == null ? null : customUpdateInfo.sha512) || (await hashFile(event.file))\n  const files = [{ url, sha512 }]\n  const result: UpdateInfo = {\n    // @ts-ignore\n    version,\n    // @ts-ignore\n    files,\n    // @ts-ignore\n    path: url /* backward compatibility, electron-updater 1.x - electron-updater 2.15.0 */,\n    // @ts-ignore\n    sha512 /* backward compatibility, electron-updater 1.x - electron-updater 2.15.0 */,\n    ...(releaseInfo as UpdateInfo),\n  }\n\n  if (customUpdateInfo != null) {\n    // file info or nsis web installer packages info\n    Object.assign(\"sha512\" in customUpdateInfo ? files[0] : result, customUpdateInfo)\n  }\n  return result\n}\n\nexport async function writeUpdateInfoFiles(updateInfoFileTasks: Array<UpdateInfoFileTask>, packager: Packager) {\n  // zip must be first and zip info must be used for old path/sha512 properties in the update info\n  updateInfoFileTasks.sort((a, b) => (a.info.files[0].url.endsWith(\".zip\") ? 0 : 100) - (b.info.files[0].url.endsWith(\".zip\") ? 0 : 100))\n\n  const updateChannelFileToInfo = new Map<string, UpdateInfoFileTask>()\n  for (const task of updateInfoFileTasks) {\n    // https://github.com/electron-userland/electron-builder/pull/2994\n    const key = `${task.file}@${safeStringifyJson(task.publishConfiguration, new Set([\"releaseType\"]))}`\n    const existingTask = updateChannelFileToInfo.get(key)\n    if (existingTask == null) {\n      updateChannelFileToInfo.set(key, task)\n      continue\n    }\n\n    existingTask.info.files.push(...task.info.files)\n  }\n\n  const releaseDate = new Date().toISOString()\n  const concurrency = 4\n  await asyncPool<UpdateInfoFileTask, void>(concurrency, Array.from(updateChannelFileToInfo.values()), async task => {\n    const publishConfig = task.publishConfiguration\n    if (publishConfig.publishAutoUpdate === false) {\n      log.debug(\n        {\n          provider: publishConfig.provider,\n          reason: \"publishAutoUpdate is set to false\",\n        },\n        \"auto update metadata file not published\"\n      )\n      return\n    }\n\n    if (task.info.releaseDate == null) {\n      task.info.releaseDate = releaseDate\n    }\n\n    const fileContent = Buffer.from(serializeToYaml(task.info, false, true))\n    await outputFile(task.file, fileContent)\n    await packager.emitArtifactCreated({\n      file: task.file,\n      fileContent,\n      arch: null,\n      packager: task.packager,\n      target: null,\n      publishConfig,\n    })\n  })\n}\n\n// backward compatibility - write json file\nasync function writeOldMacInfo(\n  publishConfig: PublishConfiguration,\n  outDir: string,\n  dir: string,\n  channel: string,\n  createdFiles: Set<string>,\n  version: string,\n  packager: PlatformPackager<any>\n) {\n  const isGitHub = publishConfig.provider === \"github\"\n  const updateInfoFile = isGitHub && outDir === dir ? path.join(dir, \"github\", `${channel}-mac.json`) : path.join(dir, `${channel}-mac.json`)\n  if (!createdFiles.has(updateInfoFile)) {\n    createdFiles.add(updateInfoFile)\n    await outputJson(\n      updateInfoFile,\n      {\n        version,\n        releaseDate: new Date().toISOString(),\n        url: computeDownloadUrl(publishConfig, packager.generateName2(\"zip\", \"mac\", isGitHub), packager),\n      },\n      { spaces: 2 }\n    )\n\n    await packager.info.emitArtifactCreated({\n      file: updateInfoFile,\n      arch: null,\n      packager,\n      target: null,\n      publishConfig,\n    })\n  }\n}\n"]}