NAME
  electron-windows-sign -- code signing for Electron apps on Windows.

SYNOPSIS
  electron-windows-sign app [options ...]

DESCRIPTION
  app
    Path to the application to sign.  It must be a directory.

  certificate-file
    Path to the certificate file (.pfx) to use for signing.

  certificate-password
    Password to use for the certificate.

  sign-tool-path
    Path to the signtool.exe binary.  If not specified, the tool will attempt
    use a built-in version.

  timestamp-server
    URL of the timestamp server to use.  If not specified, the tool will
    attempt to use a built-in server (http://timestamp.digicert.com)

  description
    Description to use for the signed files. Passed as /d to signtool.exe.

  website
    URL of the website to use for the signed files. Passed as /du to
    signtool.exe.

  sign-with-params
    Additional parameters to pass to signtool.exe.  This can be used to
    specify additional certificates to use for cross-signing.

  automatically-select-certificate
    Automatically select the best certificate to use for signing. On by default.

  help
    Print this usage information.

  debug
    Print additional debug information.
