{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../src/error.ts"], "names": [], "mappings": ";;AAAA,4BAIC;AAJD,SAAgB,QAAQ,CAAC,OAAe,EAAE,IAAY;IACpD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAC/B;IAAC,KAA+B,CAAC,IAAI,GAAG,IAAI,CAAA;IAC7C,OAAO,KAAK,CAAA;AACd,CAAC", "sourcesContent": ["export function newError(message: string, code: string) {\n  const error = new Error(message)\n  ;(error as NodeJS.ErrnoException).code = code\n  return error\n}\n"]}