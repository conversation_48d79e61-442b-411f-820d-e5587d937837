const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

let bannerWindow;
let controlWindow;

function createWindows() {
  // Create the banner window
  bannerWindow = new BrowserWindow({
    width: 1920,
    height: 200,
    transparent: true,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: false
    }
  });

  // Create the control panel window
  controlWindow = new BrowserWindow({
    width: 800,
    height: 600,
    title: 'Hookah Ads Control Panel',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: false
    }
  });

  bannerWindow.loadFile('banner.html');
  controlWindow.loadFile('control.html');

  // Position the banner at the bottom of the screen
  try {
    const { screen } = require('electron');
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    bannerWindow.setPosition(0, height - 200);
    bannerWindow.setSize(width, 200);
  } catch (error) {
    console.error('Error positioning banner window:', error);
    // Fallback positioning
    bannerWindow.setPosition(0, 800);
  }
  // When control window is closed, close the banner window and quit the app
  controlWindow.on('closed', () => {
    if (bannerWindow) {
      bannerWindow.close();
    }
    app.quit();
  });

  // If banner window is closed, close control window and quit the app
  bannerWindow.on('closed', () => {
    if (controlWindow) {
      controlWindow.close();
    }
    app.quit();
  });
}

app.whenReady().then(createWindows);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle settings updates
ipcMain.on('update-settings', (event, settings) => {
  bannerWindow.webContents.send('apply-settings', settings);
});

