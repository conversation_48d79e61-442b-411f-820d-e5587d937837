{"name": "cross-dirname", "version": "0.1.0", "type": "commonjs", "main": "./dist/cjs/index.cjs", "module": "./dist/esm/index.mjs", "types": "./dist/types/index.d.ts", "exports": {".": {"require": "./dist/cjs/index.cjs", "default": "./dist/esm/index.mjs"}}, "files": ["/dist"], "scripts": {"clear": "rm -rf dist", "build": "npm run clear && npm run build:cjs && npm run build:esm && npm run build:types", "build:cjs": "tsc && mv ./dist/cjs/index.js ./dist/cjs/index.cjs", "build:esm": "tsc --project tsconfig.esm.json && mv ./dist/esm/index.js ./dist/esm/index.mjs", "build:types": "tsc --project tsconfig.types.json", "test": "npm run test:node && npm run test:deno && npm run test:gjs && npm run test:browser", "test:node": "mocha test/node", "test:gjs": "node test/browser/esbuild.mjs && gjs -m ./test/gjs/base.test.mjs", "test:deno": "deno test ./test/deno/base.test.mjs", "test:browser": "npm run test:browser:esm && npm run test:browser:cjs", "test:browser:esm": "node test/browser/esbuild.mjs && mocha-headless-chrome --timeout 120000 --polling 1000 -f ./test/browser/index.html", "test:browser:cjs": "node test/browser/esbuild.cjs && mocha-headless-chrome --timeout 120000 --polling 1000 -f ./test/browser/index.html"}, "keywords": ["dirname", "cross-platform", "esm", "cjs", "node", "deno", "gjs"], "url": "https://github.com/JumpLink/cross-dirname", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/JumpLink/cross-dirname.git"}, "devDependencies": {"chai": "^4.3.6", "cross-dirname": "^0.0.6", "esbuild": "^0.14.49", "esm": "^3.2.25", "mocha": "^10.0.0", "mocha-headless-chrome": "^4.0.0", "typescript": "^4.7.4"}}