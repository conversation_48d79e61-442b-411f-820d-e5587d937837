{"version": 3, "file": "asarUtil.js", "sourceRoot": "", "sources": ["../../src/asar/asarUtil.ts"], "names": [], "mappings": ";;;AAAA,yCAAwE;AACxE,+CAAoD;AAEpD,4CAAiF;AACjF,+BAA8B;AAC9B,uCAAmD;AACnD,2BAA6B;AAC7B,6BAA4B;AAI5B,yDAA2E;AAC3E,qDAAqD;AAErD,gBAAgB;AAChB,MAAa,YAAY;IAOvB,YACW,QAA+B,EACvB,MAKhB;QANQ,aAAQ,GAAR,QAAQ,CAAuB;QACvB,WAAM,GAAN,MAAM,CAKtB;QAXc,eAAU,GAAG,IAAI,eAAU,EAAE,CAAA;QAa5C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;QACzD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAA;QAC1C,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAA;IAC1D,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,QAAgC;QACzC,IAAI,CAAC,0BAA0B,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAA;QAEtF,MAAM,eAAe,GAAG;YACtB,qEAAqE;YACrE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAEpB,mDAAmD;YACnD,QAAQ,CAAC,CAAC,CAAC;SACZ,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QAEnB,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAA;QAChF,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,EAAE,CAAA;QAElG,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;IACzD,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,WAAqB,EAAE,UAA8B;QACrF,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,IAAI,SAAS,CAAA;QACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,4IAA4I;YAC5I,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAA;YACnG,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAA;YACrF,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACtD,CAAC;QAED,MAAM,OAAO,GAAkB;YAC7B,MAAM,EAAE,UAAU;YAClB,SAAS,EAAE,UAAU;YACrB,QAAQ;YACR,GAAG,EAAE,IAAI;SACV,CAAA;QACD,6IAA6I;QAC7I,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAA;QACjC,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;YACxB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,kCAAkC,EAAE,CAAC;gBACnD,OAAM,CAAC,qEAAqE;YAC9E,CAAC;YACD,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,wBAAwB,CAAC,CAAA;QAC9C,CAAC,CAAA;QACD,MAAM,IAAA,+BAAwB,EAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACtF,OAAO,CAAC,GAAG,GAAG,aAAa,CAAA;IAC7B,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,QAA2B;;QACrD,MAAM,WAAW,GAAG,IAAI,+BAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAChE,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAA;QACvC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAA;QAErC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAA;QAC3C,MAAM,KAAK,GAAgB,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAG,IAAA,aAAQ,GAAE,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAA;QAEhE,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,IAAc,EAAE,gBAA6B,EAAE,EAAE;;YAClG,IAAI,MAAA,MAAA,IAAI,CAAC,MAAM,EAAC,aAAa,mDAAG,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;gBAC5C,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,WAAW,CAAC,CAAA;gBAChC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBAC1B,OAAM;YACR,CAAC;QACH,CAAC,CAAA;QACD,MAAM,yBAAyB,GAAG,KAAK,EAAE,OAMxC,EAAE,EAAE;YACH,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAA;YACrE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC7C,OAAM;YACR,CAAC;YACD,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;YAE5B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;YACrC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,IAAA,gBAAK,EAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;gBACrC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC5B,CAAC;YAED,iDAAiD;YACjD,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;gBAC5B,OAAO,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,eAAe,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;YACxE,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YAC5C,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;YACjE,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;YAC1D,IAAI,gBAAgB,EAAE,CAAC;gBACrB,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,kBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,kBAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,uDAAuD,CAAC,CAAA;gBAC5I,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,gEAAgE,CAAC,CAAA;YAC9K,CAAC;YAED,+BAA+B;YAC/B,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAA;YACtD,CAAC;YAED,kFAAkF;YAClF,IAAI,IAAI,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,CAAA;YAC/B,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1B,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YAChD,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAA;QACzC,CAAC,CAAA;QAED,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;gBAC9C,IAAA,mCAAkB,EAAC,OAAO,EAAE,aAAa,CAAC,CAAA;YAC5C,CAAC;YAED,oGAAoG;YACpG,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAA;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAC7B,MAAM,eAAe,GAAG,MAAA,OAAO,CAAC,gBAAgB,0CAAE,GAAG,CAAC,CAAC,CAAC,CAAA;gBACxD,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAA;gBAExC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAA,kCAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA;gBACjG,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAA;gBAE3E,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAA;gBACxD,WAAW,CAAC,OAAO,CAAC,yBAAyB,CAAC,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAA;gBAErG,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,sBAAiB,EAAE,CAAC;oBACjD,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;gBAChC,CAAC;YACH,CAAC;YAED,IAAI,gBAAgB,CAAC,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC,WAAW,CAAC,CAAA;gBACnF,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC7B,CAAC;iBAAM,CAAC;gBACN,4CAA4C;gBAC5C,KAAK,MAAM,EAAE,IAAI,gBAAgB,EAAE,CAAC;oBAClC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;gBACvB,CAAC;YACH,CAAC;QACH,CAAC;QACD,uCAAuC;QACvC,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAC9B,KAAK,MAAM,EAAE,IAAI,KAAK,EAAE,CAAC;YACvB,WAAW,CAAC,OAAO,CAAC,IAAA,kBAAO,EAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAA;YAE3D,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,sBAAiB,EAAE,CAAC;gBACjD,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;YAChC,CAAC;QACH,CAAC;QACD,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAC9B,OAAO;YACL,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;YACxC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;SACrC,CAAA;IACH,CAAC;CACF;AA9KD,oCA8KC;AAED,SAAS,YAAY,CAAC,OAAwB;IAC5C,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;IAE7D,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACtC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACZ,OAAO,CAAC,CAAA;QACV,CAAC;QAED,8DAA8D;QAC9D,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QACpC,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QACpC,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1B,OAAO,CAAC,CAAA;QACV,CAAC;QACD,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1B,OAAO,CAAC,CAAC,CAAA;QACX,CAAC;QAED,0BAA0B;QAC1B,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACvB,CAAC,CAAC,CAAA;IAEF,IAAI,gBAA0D,CAAA;IAC9D,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAC7B,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAA;QAE5B,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAA;QAC1C,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;YACjE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QAClC,CAAC;QAED,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACzD,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YACvC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC3B,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBACpC,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,+BAA+B,CAAC,CAAA;YACzE,CAAC;YAED,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;QACvC,CAAC;IACH,CAAC;IAED,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAA;IAE9C,OAAO;QACL,GAAG;QACH,WAAW;QACX,QAAQ;QACR,KAAK,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;QAChD,gBAAgB;KACjB,CAAA;AACH,CAAC", "sourcesContent": ["import { CreateOptions, createPackageWithOptions } from \"@electron/asar\"\nimport { AsyncTaskManager, log } from \"builder-util\"\nimport { CancellationToken } from \"builder-util-runtime\"\nimport { FileCopier, Filter, Link, MAX_FILE_REQUESTS } from \"builder-util/out/fs\"\nimport * as fs from \"fs-extra\"\nimport { mkdir, readlink, symlink } from \"fs-extra\"\nimport { platform } from \"os\"\nimport * as path from \"path\"\nimport * as tempFile from \"temp-file\"\nimport { AsarOptions } from \"../options/PlatformSpecificBuildOptions\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { ResolvedFileSet, getDestinationPath } from \"../util/appFileCopier\"\nimport { detectUnpackedDirs } from \"./unpackDetector\"\n\n/** @internal */\nexport class AsarPackager {\n  private readonly outFile: string\n  private rootForAppFilesWithoutAsar!: string\n  private readonly fileCopier = new FileCopier()\n  private readonly tmpDir: tempFile.TmpDir\n  private readonly cancellationToken: CancellationToken\n\n  constructor(\n    readonly packager: PlatformPackager<any>,\n    private readonly config: {\n      defaultDestination: string\n      resourcePath: string\n      options: AsarOptions\n      unpackPattern: Filter | undefined\n    }\n  ) {\n    this.outFile = path.join(config.resourcePath, `app.asar`)\n    this.tmpDir = packager.info.tempDirManager\n    this.cancellationToken = packager.info.cancellationToken\n  }\n\n  async pack(fileSets: Array<ResolvedFileSet>) {\n    this.rootForAppFilesWithoutAsar = await this.tmpDir.getTempDir({ prefix: \"asar-app\" })\n\n    const orderedFileSets = [\n      // Write dependencies first to minimize offset changes to asar header\n      ...fileSets.slice(1),\n\n      // Finish with the app files that change most often\n      fileSets[0],\n    ].map(orderFileSet)\n\n    const { unpackedPaths, copiedFiles } = await this.detectAndCopy(orderedFileSets)\n    const unpackGlob = unpackedPaths.length > 1 ? `{${unpackedPaths.join(\",\")}}` : unpackedPaths.pop()\n\n    await this.executeElectronAsar(copiedFiles, unpackGlob)\n  }\n\n  private async executeElectronAsar(copiedFiles: string[], unpackGlob: string | undefined) {\n    let ordering = this.config.options.ordering || undefined\n    if (!ordering) {\n      // `copiedFiles` are already ordered due to `orderedFileSets` input, so we just map to their relative paths (via substring) within the asar.\n      const filesSorted = copiedFiles.map(file => file.substring(this.rootForAppFilesWithoutAsar.length))\n      ordering = await this.tmpDir.getTempFile({ prefix: \"asar-ordering\", suffix: \".txt\" })\n      await fs.writeFile(ordering, filesSorted.join(\"\\n\"))\n    }\n\n    const options: CreateOptions = {\n      unpack: unpackGlob,\n      unpackDir: unpackGlob,\n      ordering,\n      dot: true,\n    }\n    // override logger temporarily to clean up console (electron/asar does some internal logging that blogs up the default electron-builder logs)\n    const consoleLogger = console.log\n    console.log = (...args) => {\n      if (args[0] === \"Ordering file has 100% coverage.\") {\n        return // no need to log, this means our ordering logic is working correctly\n      }\n      log.info({ args }, \"logging @electron/asar\")\n    }\n    await createPackageWithOptions(this.rootForAppFilesWithoutAsar, this.outFile, options)\n    console.log = consoleLogger\n  }\n\n  private async detectAndCopy(fileSets: ResolvedFileSet[]) {\n    const taskManager = new AsyncTaskManager(this.cancellationToken)\n    const unpackedPaths = new Set<string>()\n    const copiedFiles = new Set<string>()\n\n    const createdSourceDirs = new Set<string>()\n    const links: Array<Link> = []\n    const symlinkType = platform() === \"win32\" ? \"junction\" : \"file\"\n\n    const matchUnpacker = (file: string, dest: string, stat: fs.Stats, tmpUnpackedPaths: Set<string>) => {\n      if (this.config.unpackPattern?.(file, stat)) {\n        log.debug({ file }, \"unpacking\")\n        tmpUnpackedPaths.add(dest)\n        return\n      }\n    }\n    const writeFileOrProcessSymlink = async (options: {\n      file: string\n      destination: string\n      stat: fs.Stats\n      fileSet: ResolvedFileSet\n      transformedData: string | Buffer | undefined\n    }) => {\n      const { transformedData, file, destination, stat, fileSet } = options\n      if (!stat.isFile() && !stat.isSymbolicLink()) {\n        return\n      }\n      copiedFiles.add(destination)\n\n      const dir = path.dirname(destination)\n      if (!createdSourceDirs.has(dir)) {\n        await mkdir(dir, { recursive: true })\n        createdSourceDirs.add(dir)\n      }\n\n      // write any data if provided, skip symlink check\n      if (transformedData != null) {\n        return fs.writeFile(destination, transformedData, { mode: stat.mode })\n      }\n\n      const realPathFile = await fs.realpath(file)\n      const realPathRelative = path.relative(fileSet.src, realPathFile)\n      const isOutsidePackage = realPathRelative.startsWith(\"..\")\n      if (isOutsidePackage) {\n        log.error({ source: log.filePath(file), realPathFile: log.filePath(realPathFile) }, `unable to copy, file is symlinked outside the package`)\n        throw new Error(`Cannot copy file (${path.basename(file)}) symlinked to file (${path.basename(realPathFile)}) outside the package as that violates asar security integrity`)\n      }\n\n      // not a symlink, copy directly\n      if (file === realPathFile) {\n        return this.fileCopier.copy(file, destination, stat)\n      }\n\n      // okay, it must be a symlink. evaluate link to be relative to source file in asar\n      let link = await readlink(file)\n      if (path.isAbsolute(link)) {\n        link = path.relative(path.dirname(file), link)\n      }\n      links.push({ file: destination, link })\n    }\n\n    for (const fileSet of fileSets) {\n      if (this.config.options.smartUnpack !== false) {\n        detectUnpackedDirs(fileSet, unpackedPaths)\n      }\n\n      // Don't use Promise.all, we need to retain order of execution/iteration through the ordered fileset\n      const tmpUnpackedPaths = new Set<string>()\n      for (let i = 0; i < fileSet.files.length; i++) {\n        const file = fileSet.files[i]\n        const transformedData = fileSet.transformedFiles?.get(i)\n        const stat = fileSet.metadata.get(file)!\n\n        const relative = path.relative(this.config.defaultDestination, getDestinationPath(file, fileSet))\n        const destination = path.resolve(this.rootForAppFilesWithoutAsar, relative)\n\n        matchUnpacker(file, destination, stat, tmpUnpackedPaths)\n        taskManager.addTask(writeFileOrProcessSymlink({ transformedData, file, destination, stat, fileSet }))\n\n        if (taskManager.tasks.length > MAX_FILE_REQUESTS) {\n          await taskManager.awaitTasks()\n        }\n      }\n\n      if (tmpUnpackedPaths.size === fileSet.files.length) {\n        const relative = path.relative(this.config.defaultDestination, fileSet.destination)\n        unpackedPaths.add(relative)\n      } else {\n        // add all tmpUnpackedPaths to unpackedPaths\n        for (const it of tmpUnpackedPaths) {\n          unpackedPaths.add(it)\n        }\n      }\n    }\n    // finish copy then set up all symlinks\n    await taskManager.awaitTasks()\n    for (const it of links) {\n      taskManager.addTask(symlink(it.link, it.file, symlinkType))\n\n      if (taskManager.tasks.length > MAX_FILE_REQUESTS) {\n        await taskManager.awaitTasks()\n      }\n    }\n    await taskManager.awaitTasks()\n    return {\n      unpackedPaths: Array.from(unpackedPaths),\n      copiedFiles: Array.from(copiedFiles),\n    }\n  }\n}\n\nfunction orderFileSet(fileSet: ResolvedFileSet): ResolvedFileSet {\n  const sortedFileEntries = Array.from(fileSet.files.entries())\n\n  sortedFileEntries.sort(([, a], [, b]) => {\n    if (a === b) {\n      return 0\n    }\n\n    // Place addons last because their signature changes per build\n    const isAAddon = a.endsWith(\".node\")\n    const isBAddon = b.endsWith(\".node\")\n    if (isAAddon && !isBAddon) {\n      return 1\n    }\n    if (isBAddon && !isAAddon) {\n      return -1\n    }\n\n    // Otherwise order by name\n    return a < b ? -1 : 1\n  })\n\n  let transformedFiles: Map<number, string | Buffer> | undefined\n  if (fileSet.transformedFiles) {\n    transformedFiles = new Map()\n\n    const indexMap = new Map<number, number>()\n    for (const [newIndex, [oldIndex]] of sortedFileEntries.entries()) {\n      indexMap.set(oldIndex, newIndex)\n    }\n\n    for (const [oldIndex, value] of fileSet.transformedFiles) {\n      const newIndex = indexMap.get(oldIndex)\n      if (newIndex === undefined) {\n        const file = fileSet.files[oldIndex]\n        throw new Error(`Internal error: ${file} was lost while ordering asar`)\n      }\n\n      transformedFiles.set(newIndex, value)\n    }\n  }\n\n  const { src, destination, metadata } = fileSet\n\n  return {\n    src,\n    destination,\n    metadata,\n    files: sortedFileEntries.map(([, file]) => file),\n    transformedFiles,\n  }\n}\n"]}