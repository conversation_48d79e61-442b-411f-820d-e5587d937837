{"version": 3, "file": "hdiuil.js", "sourceRoot": "", "sources": ["../src/hdiuil.ts"], "names": [], "mappings": ";;AAEA,0BAYC;AAdD,+CAA+C;AAExC,KAAK,UAAU,OAAO,CAAC,IAAc;IAC1C,OAAO,IAAA,oBAAK,EACV,GAAG,EAAE,CAAC,IAAA,mBAAI,EAAC,SAAS,EAAE,IAAI,CAAC,EAC3B,CAAC,EACD,IAAI,EACJ,IAAI,EACJ,CAAC,EACD,CAAC,KAAU,EAAE,EAAE;QACb,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,2BAA2B,CAAC,CAAA;QAC9G,OAAO,IAAI,CAAA;IACb,CAAC,CACF,CAAA;AACH,CAAC", "sourcesContent": ["import { exec, log, retry } from \"builder-util\"\n\nexport async function hdiUtil(args: string[]) {\n  return retry(\n    () => exec(\"hdiutil\", args),\n    5,\n    5000,\n    2000,\n    0,\n    (error: any) => {\n      log.error({ args, code: error.code, error: (error.message || error).toString() }, \"unable to execute hdiutil\")\n      return true\n    }\n  )\n}\n"]}