{"version": 3, "file": "WebInstallerTarget.js", "sourceRoot": "", "sources": ["../../../src/targets/nsis/WebInstallerTarget.ts"], "names": [], "mappings": ";;;AAAA,+CAAwC;AACxC,iEAAoH;AAGpH,6CAAyC;AAGzC,eAAe;AACf,MAAa,kBAAmB,SAAQ,uBAAU;IAChD,YAAY,QAAqB,EAAE,MAAc,EAAE,UAAkB,EAAE,aAA+B;QACpG,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,CAAC,CAAA;IACpD,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAA;IACb,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,QAAiB,EAAE,OAAY;QAC9D,8BAA8B;QAC9B,MAAO,uBAAU,CAAC,SAAgC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QAEjG,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAyB,CAAA;QAE9C,IAAI,aAAa,GAAG,OAAO,CAAC,aAAa,CAAA;QACzC,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,cAAc,GAAG,MAAM,IAAA,+CAA8B,EAAC,QAAQ,EAAE,MAAM,IAAA,kCAAiB,EAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,CAAA;YACjJ,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAA;YAC5D,CAAC;YAED,aAAa,GAAG,IAAA,mCAAkB,EAAC,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;QACvE,CAAC;QAED,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAA;QAC5C,OAAO,CAAC,eAAe,GAAG,aAAa,CAAA;IACzC,CAAC;IAED,IAAI,6BAA6B;QAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,KAAK,KAAK,EAAE,CAAC;YACnD,kBAAG,CAAC,IAAI,CAAC,EAAE,uBAAuB,EAAE,IAAI,EAAE,EAAE,iFAAiF,CAAC,CAAA;QAChI,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAES,wBAAwB,CAAC,YAA0B,EAAE,YAAqB;QAClF,OAAO,4CAA4C,CAAA;IACrD,CAAC;IAES,2BAA2B;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAA;QACzF,OAAO,GAAG,OAAO,CAAC,IAAI,IAAI,UAAU,IAAI,OAAO,CAAC,OAAO,MAAM,CAAA;IAC/D,CAAC;CACF;AA9CD,gDA8CC", "sourcesContent": ["import { Arch, log } from \"builder-util\"\nimport { computeDownloadUrl, getPublishConfigs, getPublishConfigsForUpdateInfo } from \"../../publish/PublishManager\"\nimport { WinPackager } from \"../../winPackager\"\nimport { NsisWebOptions } from \"./nsisOptions\"\nimport { NsisTarget } from \"./NsisTarget\"\nimport { AppPackageHelper } from \"./nsisUtil\"\n\n/** @private */\nexport class WebInstallerTarget extends NsisTarget {\n  constructor(packager: WinPackager, outDir: string, targetName: string, packageHelper: AppPackageHelper) {\n    super(packager, outDir, targetName, packageHelper)\n  }\n\n  get isWebInstaller(): boolean {\n    return true\n  }\n\n  protected async configureDefines(oneClick: boolean, defines: any): Promise<any> {\n    //noinspection ES6MissingAwait\n    await (NsisTarget.prototype as WebInstallerTarget).configureDefines.call(this, oneClick, defines)\n\n    const packager = this.packager\n    const options = this.options as NsisWebOptions\n\n    let appPackageUrl = options.appPackageUrl\n    if (appPackageUrl == null) {\n      const publishConfigs = await getPublishConfigsForUpdateInfo(packager, await getPublishConfigs(packager, packager.info.config, null, false), null)\n      if (publishConfigs == null || publishConfigs.length === 0) {\n        throw new Error(\"Cannot compute app package download URL\")\n      }\n\n      appPackageUrl = computeDownloadUrl(publishConfigs[0], null, packager)\n    }\n\n    defines.APP_PACKAGE_URL_IS_INCOMPLETE = null\n    defines.APP_PACKAGE_URL = appPackageUrl\n  }\n\n  get shouldBuildUniversalInstaller() {\n    if (this.options.buildUniversalInstaller === false) {\n      log.warn({ buildUniversalInstaller: true }, \"only universal builds are supported for nsis-web installers, overriding setting\")\n    }\n    return true\n  }\n\n  protected installerFilenamePattern(_primaryArch?: Arch | null, _defaultArch?: string): string {\n    return \"${productName} Web Setup ${version}.${ext}\"\n  }\n\n  protected generateGitHubInstallerName(): string {\n    const appInfo = this.packager.appInfo\n    const classifier = appInfo.name.toLowerCase() === appInfo.name ? \"web-setup\" : \"WebSetup\"\n    return `${appInfo.name}-${classifier}-${appInfo.version}.exe`\n  }\n}\n"]}