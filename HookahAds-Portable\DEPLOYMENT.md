# Deployment Guide - Hookah Ads

## Quick Deployment (Recommended)

### Option 1: Use Pre-built Portable Executable

1. **Build the portable app** (on development machine):
   ```bash
   # Double-click: build-portable.bat
   # OR run: npm run build-portable
   ```

2. **Copy to destination computer**:
   - Copy `dist/HookahAds-Portable.exe` to the target computer
   - That's it! No installation required.

3. **Run on destination computer**:
   - Double-click `HookahAds-Portable.exe`
   - The app will start immediately

### Option 2: Copy Entire Project Folder

If you prefer to run from source code:

1. **Copy the entire project folder** to the destination computer
2. **Install Node.js** on the destination computer (if not already installed)
3. **Run the app**:
   ```bash
   cd hookah-ads
   npm install
   npm start
   ```

## Detailed Deployment Steps

### For Development Machine (Building)

1. **Prepare the build environment**:
   ```bash
   # Ensure all dependencies are installed
   npm install
   
   # Test the app works
   npm start
   ```

2. **Build portable executable**:
   ```bash
   # Build Windows portable
   npm run build-portable
   
   # OR build for all platforms
   npm run build-all
   ```

3. **Locate built files**:
   - Windows Portable: `dist/HookahAds-Portable.exe`
   - Windows Installer: `dist/HookahAds-Setup.exe`
   - Linux AppImage: `dist/HookahAds-x.x.x.AppImage`
   - macOS DMG: `dist/HookahAds-x.x.x.dmg`

### For Destination Computer (Deployment)

#### Method 1: Portable Executable (No Installation)
```
1. Copy HookahAds-Portable.exe to any folder
2. Double-click to run
3. Done!
```

#### Method 2: Full Installation
```
1. Copy HookahAds-Setup.exe to the computer
2. Run the installer
3. Follow installation wizard
4. Launch from Start Menu or Desktop shortcut
```

#### Method 3: Source Code Deployment
```
1. Install Node.js (https://nodejs.org/)
2. Copy entire hookah-ads folder
3. Open Command Prompt in the folder
4. Run: npm install
5. Run: npm start
```

## File Structure for Deployment

### Minimal Deployment (Portable)
```
destination-computer/
└── HookahAds-Portable.exe    # Single file - ready to run
```

### Full Source Deployment
```
destination-computer/
└── hookah-ads/
    ├── main.js
    ├── banner.html
    ├── control.html
    ├── package.json
    ├── package-lock.json
    └── node_modules/          # Created by npm install
```

## System Requirements

### Minimum Requirements
- **OS**: Windows 7/8/10/11 (64-bit)
- **RAM**: 512MB available
- **Storage**: 200MB free space
- **Display**: Any resolution

### Recommended Requirements
- **OS**: Windows 10/11 (64-bit)
- **RAM**: 2GB available
- **Storage**: 1GB free space
- **Display**: 1920x1080 or higher

## Troubleshooting Deployment

### Common Issues

#### "App won't start"
- **Solution**: Install Visual C++ Redistributable
- **Download**: https://aka.ms/vs/17/release/vc_redist.x64.exe

#### "Missing DLL errors"
- **Solution**: Use the installer version instead of portable
- **Alternative**: Install Node.js and run from source

#### "Antivirus blocking the app"
- **Solution**: Add exception for HookahAds-Portable.exe
- **Note**: This is common with unsigned executables

#### "App starts but banner doesn't show"
- **Check**: Windows display scaling settings
- **Solution**: Set display scaling to 100% or restart app

### Performance Optimization

#### For Better Performance
1. **Close unnecessary applications**
2. **Use SSD storage** if available
3. **Ensure adequate RAM** (2GB+)
4. **Update graphics drivers**

#### For Multiple Monitors
- The banner will appear on the primary monitor
- To change primary monitor: Windows Settings > Display

## Security Considerations

### Antivirus Software
- Some antivirus programs may flag the portable executable
- This is normal for unsigned applications
- Add the app to antivirus exceptions if needed

### Windows SmartScreen
- Windows may show "Unknown publisher" warning
- Click "More info" → "Run anyway" to proceed
- This is normal for unsigned applications

### Network Access
- The app doesn't require internet connection
- No data is transmitted outside the local computer
- Safe for use in secure environments

## Updates and Maintenance

### Updating the App
1. **Build new version** on development machine
2. **Copy new executable** to destination computer
3. **Replace old version** (app will retain settings)

### Backup Settings
- Settings are stored in the app's memory during runtime
- No persistent settings file is created
- Settings reset when app is restarted

## Support

### Getting Help
- Check README.md for basic usage instructions
- Review this deployment guide for setup issues
- Contact development team for technical support

### Reporting Issues
When reporting problems, include:
- Operating system version
- App version
- Error messages (if any)
- Steps to reproduce the issue
