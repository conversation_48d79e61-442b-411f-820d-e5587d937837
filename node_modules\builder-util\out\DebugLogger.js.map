{"version": 3, "file": "DebugLogger.js", "sourceRoot": "", "sources": ["../src/DebugLogger.ts"], "names": [], "mappings": ";;;AAAA,uCAAqC;AACrC,iCAAwC;AACxC,qCAAsC;AAEtC,MAAa,WAAW;IAGtB,YAAqB,YAAY,IAAI;QAAhB,cAAS,GAAT,SAAS,CAAO;QAF5B,SAAI,GAAG,IAAI,GAAG,EAAe,CAAA;IAEE,CAAC;IAEzC,GAAG,CAAC,GAAW,EAAE,KAAU;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAM;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC/B,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAA;QACjB,IAAI,QAAQ,GAAkB,IAAI,CAAA;QAClC,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;gBACxC,QAAQ,GAAG,CAAC,CAAA;gBACZ,MAAK;YACP,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;oBACd,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,EAAe,CAAC,CAAA;gBAClC,CAAC;qBAAM,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACxC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACtB,CAAC;gBACD,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;YACd,CAAC;QACH,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,QAAS,CAAC,CAAC,EAAE,CAAC;YACpC,CAAC,CAAC,GAAG,CAAC,QAAS,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAS,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;QAChD,CAAC;aAAM,CAAC;YACN,CAAC,CAAC,GAAG,CAAC,QAAS,EAAE,KAAK,CAAC,CAAA;QACzB,CAAC;IACH,CAAC;IAED,IAAI,CAAC,IAAY;QACf,MAAM,IAAI,GAAG,IAAA,oBAAW,EAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACnC,uEAAuE;QACvE,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnD,OAAO,IAAA,qBAAU,EAAC,IAAI,EAAE,IAAA,sBAAe,EAAC,IAAI,CAAC,CAAC,CAAA;QAChD,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC1B,CAAC;IACH,CAAC;CACF;AA3CD,kCA2CC", "sourcesContent": ["import { outputFile } from \"fs-extra\"\nimport { serializeToYaml } from \"./util\"\nimport { mapToObject } from \"./mapper\"\n\nexport class DebugLogger {\n  readonly data = new Map<string, any>()\n\n  constructor(readonly isEnabled = true) {}\n\n  add(key: string, value: any) {\n    if (!this.isEnabled) {\n      return\n    }\n\n    const dataPath = key.split(\".\")\n    let o = this.data\n    let lastName: string | null = null\n    for (const p of dataPath) {\n      if (p === dataPath[dataPath.length - 1]) {\n        lastName = p\n        break\n      } else {\n        if (!o.has(p)) {\n          o.set(p, new Map<string, any>())\n        } else if (typeof o.get(p) === \"string\") {\n          o.set(p, [o.get(p)])\n        }\n        o = o.get(p)\n      }\n    }\n\n    if (Array.isArray(o.get(lastName!))) {\n      o.set(lastName!, [...o.get(lastName!), value])\n    } else {\n      o.set(lastName!, value)\n    }\n  }\n\n  save(file: string) {\n    const data = mapToObject(this.data)\n    // toml and json doesn't correctly output multiline string as multiline\n    if (this.isEnabled && Object.keys(data).length > 0) {\n      return outputFile(file, serializeToYaml(data))\n    } else {\n      return Promise.resolve()\n    }\n  }\n}\n"]}