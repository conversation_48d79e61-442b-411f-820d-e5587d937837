# Changelog

## v0.4.1

- Cherry-pick [v.1.0.1](https://github.com/jet2jet/pe-library-js/releases/tag/v1.0.1) bugfix

## v0.4.0

- Add support for ES module loading in Node.js environment

## v0.3.0

- Add support for 'unparsable' resource data
  - Use `ignoreUnparsableData` parameter on `from` method of `NtExecutableResource`.

## v0.2.1

- Fix version information (no classes/types are changed)

## v0.2.0

- Add `NtExecutableResource` class and related types

## v0.1.3

- Export `calculateCheckSumForPE` function

## v0.1.2

- Remove `@internal` specifier for some methods

## v0.1.1

- Export `NtExecutableSection` and `NtExecutableFromOptions` types

## v0.1.0

Initial version (almost cloned from [resedit-js](https://github.com/jet2jet/resedit-js))
