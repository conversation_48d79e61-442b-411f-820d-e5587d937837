{"name": "hookah-ads", "version": "1.0.0", "description": "Hookah lounge advertisement overlay", "main": "main.js", "author": "Hookah Ads", "scripts": {"start": "electron .", "build": "electron-builder", "build-portable": "electron-builder --win portable", "build-all": "electron-builder --win --linux --mac", "dist": "npm run build-portable"}, "build": {"appId": "com.hookahads.app", "productName": "Hookah Ads", "directories": {"output": "dist"}, "files": ["main.js", "banner.html", "control.html", "package.json"], "compression": "normal", "nsis": {"artifactName": "HookahAds-Setup.exe", "oneClick": false, "allowToChangeInstallationDirectory": true}, "win": {"target": [{"target": "portable", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "sign": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "portable": {"artifactName": "HookahAds-Portable.exe"}}, "devDependencies": {"electron": "^24.3.0", "electron-builder": "^24.13.3"}}