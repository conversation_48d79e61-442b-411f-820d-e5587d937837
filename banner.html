<!DOCTYPE html>
<html>
<head>
  <title>Banner Display</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background-color: transparent;
    }
    #banner {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
    }
    #content {
      white-space: nowrap;
      position: absolute;
      left: 100%;
      animation: scroll 20s linear infinite;
    }
    #content img {
      height: 80%;
      max-height: 150px;
      margin: 0 20px;
      vertical-align: middle;
      object-fit: contain;
    }
    @keyframes scroll {
      from { transform: translateX(0); }
      to { transform: translateX(calc(-100vw - 100%)); }
    }
  </style>
</head>
<body>
  <div id="banner">
    <div id="content"></div>
  </div>
  <script>
    const { ipcRenderer } = require('electron');

    ipcRenderer.on('apply-settings', (event, settings) => {
      try {
        const banner = document.getElementById('banner');
        const content = document.getElementById('content');

        // Apply banner height
        if (settings.height) {
          banner.style.height = settings.height + 'px';
        }

        // Apply colors
        if (settings.backgroundColor) {
          banner.style.backgroundColor = settings.backgroundColor;
        }
        if (settings.textColor) {
          content.style.color = settings.textColor;
        }

        // Apply font size (percentage of banner height)
        if (settings.fontSize && settings.height) {
          const fontSize = (settings.height * settings.fontSize / 100);
          content.style.fontSize = fontSize + 'px';
          content.style.lineHeight = settings.height + 'px';
        }

        // Create content sequence: Text → Images → Text → Images...
        content.innerHTML = '';

        // Create the repeating sequence
        const createSequence = () => {
          const sequence = document.createElement('div');
          sequence.style.display = 'inline-flex';
          sequence.style.alignItems = 'center';
          sequence.style.whiteSpace = 'nowrap';

          // Add text first
          if (settings.text) {
            const textSpan = document.createElement('span');
            textSpan.textContent = settings.text;
            textSpan.style.marginRight = '40px'; // Space after text
            sequence.appendChild(textSpan);
          }

          // Add images after text
          if (settings.images && Array.isArray(settings.images)) {
            settings.images.forEach(img => {
              if (img) {
                const imgElement = document.createElement('img');

                // Clean the path and try different formats
                let cleanPath = img.replace(/\\/g, '/');

                // Try different URL formats
                const urlFormats = [
                  `file:///${cleanPath}`,
                  `file://${cleanPath}`,
                  cleanPath
                ];

                let formatIndex = 0;

                const tryNextFormat = () => {
                  if (formatIndex < urlFormats.length) {
                    imgElement.src = urlFormats[formatIndex];
                    console.log(`Trying image format ${formatIndex + 1}:`, urlFormats[formatIndex]);
                    formatIndex++;
                  } else {
                    console.error('All image formats failed for:', img);
                    // Instead of hiding, show a placeholder
                    imgElement.alt = '[Image]';
                    imgElement.style.display = 'inline-block';
                    imgElement.style.width = '100px';
                    imgElement.style.height = '60px';
                    imgElement.style.backgroundColor = '#ddd';
                    imgElement.style.border = '2px solid #999';
                    imgElement.style.textAlign = 'center';
                    imgElement.style.lineHeight = '60px';
                    imgElement.style.fontSize = '12px';
                    imgElement.style.color = '#666';
                  }
                };

                imgElement.onerror = function() {
                  console.log('Image format failed:', this.src);
                  tryNextFormat();
                };

                imgElement.onload = function() {
                  console.log('Successfully loaded image:', this.src);
                };

                // Set image styles
                imgElement.alt = 'ad';
                imgElement.style.height = '80%';
                imgElement.style.maxHeight = '150px';
                imgElement.style.margin = '0 20px';
                imgElement.style.verticalAlign = 'middle';
                imgElement.style.objectFit = 'contain';

                // Start trying formats
                tryNextFormat();

                sequence.appendChild(imgElement);
              }
            });
          }

          // Add spacing after the complete sequence
          const spacer = document.createElement('span');
          spacer.style.marginRight = '60px'; // Space before next repetition
          sequence.appendChild(spacer);

          return sequence;
        };

        // Create multiple sequences for continuous scrolling
        for (let i = 0; i < 3; i++) {
          content.appendChild(createSequence());
        }

        // Update animation speed
        const speed = settings.scrollSpeed || 20;
        content.style.animation = `scroll ${speed}s linear infinite`;
      } catch (error) {
        console.error('Error applying settings:', error);
      }
    });
  </script>
</body>
</html>