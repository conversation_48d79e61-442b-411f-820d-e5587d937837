<!DOCTYPE html>
<html>
<head>
  <title>Banner Display</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background-color: transparent;
    }
    #banner {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
    }
    #content {
      white-space: nowrap;
      position: absolute;
      left: 100%;
      animation: scroll 20s linear infinite;
    }
    #content img {
      height: 80%;
      max-height: 150px;
      margin: 0 20px;
      vertical-align: middle;
      object-fit: contain;
    }
    @keyframes scroll {
      from { transform: translateX(0); }
      to { transform: translateX(calc(-100vw - 100%)); }
    }
  </style>
</head>
<body>
  <div id="banner">
    <div id="content"></div>
  </div>
  <script>
    const { ipcRenderer } = require('electron');

    ipcRenderer.on('apply-settings', (event, settings) => {
      try {
        const banner = document.getElementById('banner');
        const content = document.getElementById('content');

        // Apply banner height
        if (settings.height) {
          banner.style.height = settings.height + 'px';
        }

        // Apply colors
        if (settings.backgroundColor) {
          banner.style.backgroundColor = settings.backgroundColor;
        }
        if (settings.textColor) {
          content.style.color = settings.textColor;
        }

        // Apply font size (percentage of banner height)
        if (settings.fontSize && settings.height) {
          const fontSize = (settings.height * settings.fontSize / 100);
          content.style.fontSize = fontSize + 'px';
          content.style.lineHeight = settings.height + 'px';
        }

        // Create simple sequence: Text → Images → Text → Images...
        content.innerHTML = '';

        console.log('Creating banner content with settings:', settings);

        // Add text first
        if (settings.text) {
          const textSpan = document.createElement('span');
          textSpan.textContent = settings.text;
          textSpan.style.marginRight = '60px'; // Space after text
          textSpan.style.display = 'inline-block';
          content.appendChild(textSpan);
          console.log('Added text:', settings.text);
        }

        // Add images after text
        if (settings.images && Array.isArray(settings.images) && settings.images.length > 0) {
          console.log('Processing images:', settings.images);

          settings.images.forEach((img, index) => {
            if (img) {
              console.log(`Creating image ${index + 1}:`, img);

              // Create a simple image element
              const imgElement = document.createElement('img');

              // Set basic properties first
              imgElement.style.height = '80%';
              imgElement.style.maxHeight = '150px';
              imgElement.style.margin = '0 20px';
              imgElement.style.verticalAlign = 'middle';
              imgElement.style.objectFit = 'contain';
              imgElement.style.display = 'inline-block';
              imgElement.style.backgroundColor = '#f0f0f0';
              imgElement.style.border = '1px solid #ccc';

              // Try to load the image
              imgElement.onerror = function() {
                console.log('Image failed to load, showing placeholder:', img);
                // Create a visible placeholder
                this.style.width = '120px';
                this.style.height = '80px';
                this.style.backgroundColor = '#e0e0e0';
                this.style.border = '2px dashed #999';
                this.alt = `[Image ${index + 1}]`;
                this.title = `Image: ${img.split('\\').pop() || img.split('/').pop()}`;
              };

              imgElement.onload = function() {
                console.log('Image loaded successfully:', img);
              };

              // Set the source - try the simplest approach first
              imgElement.src = img;
              imgElement.alt = `Image ${index + 1}`;

              content.appendChild(imgElement);
              console.log(`Added image element ${index + 1} to content`);
            }
          });
        } else {
          console.log('No images to process');
        }

        // Add the same sequence again for continuous scrolling
        if (settings.text) {
          const textSpan2 = document.createElement('span');
          textSpan2.textContent = settings.text;
          textSpan2.style.marginLeft = '60px'; // Space before repeated text
          textSpan2.style.marginRight = '60px'; // Space after repeated text
          textSpan2.style.display = 'inline-block';
          content.appendChild(textSpan2);
        }

        // Add images again for continuous scrolling
        if (settings.images && Array.isArray(settings.images) && settings.images.length > 0) {
          settings.images.forEach((img, index) => {
            if (img) {
              const imgElement = document.createElement('img');
              imgElement.style.height = '80%';
              imgElement.style.maxHeight = '150px';
              imgElement.style.margin = '0 20px';
              imgElement.style.verticalAlign = 'middle';
              imgElement.style.objectFit = 'contain';
              imgElement.style.display = 'inline-block';
              imgElement.style.backgroundColor = '#f0f0f0';
              imgElement.style.border = '1px solid #ccc';

              imgElement.onerror = function() {
                this.style.width = '120px';
                this.style.height = '80px';
                this.style.backgroundColor = '#e0e0e0';
                this.style.border = '2px dashed #999';
                this.alt = `[Image ${index + 1}]`;
                this.title = `Image: ${img.split('\\').pop() || img.split('/').pop()}`;
              };

              imgElement.src = img;
              imgElement.alt = `Image ${index + 1}`;
              content.appendChild(imgElement);
            }
          });
        }

        // Update animation speed
        const speed = settings.scrollSpeed || 20;
        content.style.animation = `scroll ${speed}s linear infinite`;
      } catch (error) {
        console.error('Error applying settings:', error);
      }
    });
  </script>
</body>
</html>