directories:
  output: dist
  buildResources: build
appId: com.hookahads.app
productName: Hookah Ads
files:
  - filter:
      - main.js
      - banner.html
      - control.html
      - package.json
win:
  target:
    - target: portable
      arch:
        - x64
    - target: nsis
      arch:
        - x64
linux:
  target:
    - target: AppImage
      arch:
        - x64
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
portable:
  artifactName: HookahAds-Portable.exe
nsis:
  artifactName: HookahAds-Setup.exe
  oneClick: false
  allowToChangeInstallationDirectory: true
electronVersion: 36.4.0
