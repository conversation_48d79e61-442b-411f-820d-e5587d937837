{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/index.ts"], "names": [], "mappings": ";;;AAcA,oEAeC;AAED,wCAGC;AAlCD,uEAAmE;AACnE,yEAAqE;AACrE,yEAAqE;AACrE,qDAAuE;AAiC9D,uFAjCA,uBAAM,OAiCA;AAAE,yGAjCI,yCAAwB,OAiCJ;AA/BzC,+CAAmC;AAEnC,KAAK,UAAU,oBAAoB,CAAC,OAAe;IACjD,MAAM,OAAO,GAAG,MAAM,mDAAwB,CAAC,SAAS,CAAC,KAAK,CAAA;IAC9D,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAI,EAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;IACrF,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;IACpG,OAAO,KAAK,CAAC,aAAa,CAAC,KAAK,SAAS,CAAA;AAC3C,CAAC;AAEM,KAAK,UAAU,4BAA4B,CAAC,OAAe;IAChE,MAAM,OAAO,GAAO,MAAM,IAAA,uBAAM,EAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAA;IAClD,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,MAAM;YACT,IAAI,MAAM,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxC,OAAO,IAAI,iDAAuB,CAAC,OAAO,CAAC,CAAA;YAC7C,CAAC;YACD,OAAO,IAAI,mDAAwB,CAAC,OAAO,CAAC,CAAA;QAC9C,KAAK,KAAK;YACR,OAAO,IAAI,iDAAuB,CAAC,OAAO,CAAC,CAAA;QAC7C,KAAK,MAAM;YACT,OAAO,IAAI,mDAAwB,CAAC,OAAO,CAAC,CAAA;QAC9C;YACE,OAAO,IAAI,iDAAuB,CAAC,OAAO,CAAC,CAAA;IAC/C,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,cAAc,CAAC,OAAe;IAClD,MAAM,SAAS,GAAG,MAAM,4BAA4B,CAAC,OAAO,CAAC,CAAA;IAC7D,OAAO,SAAS,CAAC,cAAc,EAAE,CAAA;AACnC,CAAC", "sourcesContent": ["import { NpmNodeModulesCollector } from \"./npmNodeModulesCollector\"\nimport { PnpmNodeModulesCollector } from \"./pnpmNodeModulesCollector\"\nimport { YarnNodeModulesCollector } from \"./yarnNodeModulesCollector\"\nimport { detect, PM, getPackageManagerVersion } from \"./packageManager\"\nimport { NodeModuleInfo } from \"./types\"\nimport { exec } from \"builder-util\"\n\nasync function isPnpmProjectHoisted(rootDir: string) {\n  const command = await PnpmNodeModulesCollector.pmCommand.value\n  const config = await exec(command, [\"config\", \"list\"], { cwd: rootDir, shell: true })\n  const lines = Object.fromEntries(config.split(\"\\n\").map(line => line.split(\"=\").map(s => s.trim())))\n  return lines[\"node-linker\"] === \"hoisted\"\n}\n\nexport async function getCollectorByPackageManager(rootDir: string) {\n  const manager: PM = await detect({ cwd: rootDir })\n  switch (manager) {\n    case \"pnpm\":\n      if (await isPnpmProjectHoisted(rootDir)) {\n        return new NpmNodeModulesCollector(rootDir)\n      }\n      return new PnpmNodeModulesCollector(rootDir)\n    case \"npm\":\n      return new NpmNodeModulesCollector(rootDir)\n    case \"yarn\":\n      return new YarnNodeModulesCollector(rootDir)\n    default:\n      return new NpmNodeModulesCollector(rootDir)\n  }\n}\n\nexport async function getNodeModules(rootDir: string): Promise<NodeModuleInfo[]> {\n  const collector = await getCollectorByPackageManager(rootDir)\n  return collector.getNodeModules()\n}\n\nexport { detect, getPackageManagerVersion, PM }\n"]}