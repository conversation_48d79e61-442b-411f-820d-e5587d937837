{"version": 3, "file": "windowsSignToolManager.js", "sourceRoot": "", "sources": ["../../src/codeSign/windowsSignToolManager.ts"], "names": [], "mappings": ";;;AAoBA,8CAEC;AAkcD,8BAGC;AA3dD,+CAA6E;AAC7E,+DAAwD;AACxD,uCAAiC;AACjC,uCAA+B;AAC/B,yBAAwB;AACxB,6BAA4B;AAC5B,gDAAuC;AAGvC,sDAA8C;AAC9C,mDAA4D;AAC5D,qDAA8D;AAC9D,yCAAmD;AACnD,6CAAiD;AACjD,iCAAoC;AAEpC,yCAA8C;AAI9C,SAAgB,iBAAiB;IAC/B,OAAO,IAAA,oBAAM,EAAC,aAAa,CAAC,CAAA;AAC9B,CAAC;AA8CD,MAAa,sBAAsB;IAGjC,YAA6B,QAAqB;QAArB,aAAQ,GAAR,QAAQ,CAAa;QAIzC,0BAAqB,GAAG,IAAI,eAAI,CAAuB,KAAK,IAAI,EAAE;;YACzE,MAAM,aAAa,GAAG,MAAA,IAAI,CAAC,4BAA4B,CAAC,eAAe,0CAAE,aAAa,CAAA;YACtF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAA;YACb,CAAC;iBAAM,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;gBACjC,OAAO,IAAA,sBAAO,EAAC,aAAa,CAAC,CAAA;YAC/B,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAA;YAC9C,OAAO,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;QAEO,iBAAY,GAAG,IAAI,+BAAQ,CAClC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAClB,KAAK,EAAC,GAAG,EAAC,EAAE;YACV,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,KAAK,CAAA;YAC/B,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAA;YACb,CAAC;YAED,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;gBACzB,MAAM,wBAAwB,GAAG,OAAO,CAAC,OAAO,CAAA;gBAChD,OAAO;oBACL,UAAU,EAAE,IAAA,8BAAO,EAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAE;oBACxD,wBAAwB;iBACzB,CAAA;YACH,CAAC;YAED,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAA;YAC5B,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAA;YACb,CAAC;YACD,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAA;QAChE,CAAC,CACF,CAAA;QAEQ,YAAO,GAAG,IAAI,+BAAQ,CAC7B,GAAG,EAAE,CAAC,IAAI,CAAC,4BAA4B,EACvC,4BAA4B,CAAC,EAAE;;YAC7B,MAAM,WAAW,GAAG,MAAA,4BAA4B,CAAC,eAAe,0CAAE,sBAAsB,CAAA;YACxF,MAAM,OAAO,GAAG,MAAA,4BAA4B,CAAC,eAAe,0CAAE,eAAe,CAAA;YAC7E,IAAI,WAAW,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBAC3C,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK;qBAC1B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;qBAC9E,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;;oBAChB,kEAAkE;oBAClE,IAAI,CAAA,MAAA,4BAA4B,CAAC,eAAe,0CAAE,IAAI,KAAI,IAAI,EAAE,CAAC;wBAC/D,MAAM,CAAC,CAAA;oBACT,CAAC;yBAAM,CAAC;wBACN,kBAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,mCAAmC,CAAC,CAAA;wBAC5D,OAAO,IAAI,CAAA;oBACb,CAAC;gBACH,CAAC,CAAC,CAAA;YACN,CAAC;YAED,MAAM,eAAe,GAAG,MAAA,4BAA4B,CAAC,eAAe,0CAAE,eAAe,CAAA;YACrF,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;gBAC5B,MAAM,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;gBAC1D,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,mBAAmB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,EAAE;iBAC1E,CAAC,CAAA;YACJ,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,CAAA;YACxD,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC;gBACtC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC9B,CAAC;YAED,OAAO,CACL,IAAA,4BAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACrF,cAAc;iBACb,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;gBAChB,IAAI,CAAC,YAAY,wCAAyB,EAAE,CAAC;oBAC3C,MAAM,IAAI,wCAAyB,CAAC,oDAAoD,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;gBACtG,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,CAAA;gBACT,CAAC;YACH,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,EAAE;gBACX,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;iBACzC,CAAA;YACH,CAAC,CAAC,CACL,CAAA;QACH,CAAC,CACF,CAAA;QA1FC,IAAI,CAAC,4BAA4B,GAAG,QAAQ,CAAC,4BAA4B,CAAA;IAC3E,CAAC;IA2FD,UAAU;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED,2FAA2F;IAC3F,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,aAAqB;QAC9D,IAAI,MAAM,YAAY,oBAAU,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;YACvE,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,0BAA0B,EAAE,EAAE,oBAAoB,CAAC,CAAA;YACtE,OAAO,aAAa,IAAI,OAAO,CAAA;QACjC,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAA;QAC9C,MAAM,SAAS,GAAG,aAAa,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAA;QAChG,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAA;QAClF,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAA2B;;QACxC,IAAI,MAAM,GAAG,MAAA,OAAO,CAAC,OAAO,CAAC,eAAe,0CAAE,qBAAqB,CAAA;QACnE,oCAAoC;QACpC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,MAAM,GAAG,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QAC3E,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1C,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAA;QACrB,CAAC;aAAM,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;QAC7B,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QACpD,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAA;QAC9C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAA;QAE5D,MAAM,UAAU,GAAG,MAAM,IAAA,yBAAe,EAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,MAAA,OAAO,CAAC,OAAO,CAAC,eAAe,0CAAE,IAAI,EAAE,MAAM,CAAC,CAAA;QAEnH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA;QACxC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,GAAQ;gBACjB,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;aACjC,CAAA;YACD,IAAI,MAAM,IAAI,OAAO,EAAE,CAAC;gBACtB,OAAO,GAAG;oBACR,GAAG,OAAO;oBACV,eAAe,EAAE,OAAO,CAAC,IAAI;iBAC9B,CAAA;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG;oBACR,GAAG,OAAO;oBACV,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,IAAI,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,cAAc;iBACrE,CAAA;YACH,CAAC;YACD,kBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;QAC9B,CAAC;aAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACvB,kBAAG,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,gDAAgD,CAAC,CAAA;YAChG,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,UAAU,IAAI,CAAC,CAAC,MAA0C,EAAE,QAAqB,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;QACrI,IAAI,MAAM,GAAG,KAAK,CAAA;QAClB,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;YAC1B,MAAM,iBAAiB,GAAiC,EAAE,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAA;YACzG,MAAM,OAAO,CAAC,OAAO,CACnB,QAAQ,CACN;gBACE,GAAG,iBAAiB;gBACpB,mBAAmB,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,KAAK,CAAC;aACjF,EACD,IAAI,CAAC,QAAQ,CACd,CACF,CAAA;YACD,MAAM,GAAG,IAAI,CAAA;YACb,IAAI,iBAAiB,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;gBAC/C,MAAM,IAAA,iBAAM,EAAC,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;YAChE,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,QAAgB;QAC9C,IAAI,MAAM,GAAQ,IAAI,CAAA;QACtB,MAAM,kBAAkB,GAAG,4GAA4G,CAAA;QACvI,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,IAAA,oCAAuB,EAAM,CAAC,kBAAkB,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAA;QAC5G,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,GAAG,kBAAkB,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;QACzD,CAAC;QAED,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YACzB,wCAAwC;YACxC,MAAM,IAAI,wCAAyB,CAAC,GAAG,kBAAkB,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAA;QAC7E,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,qEAAqE;IACrE,mBAAmB,CAAC,OAAqC,EAAE,KAAc,EAAE,KAAgB,IAAI,cAAS,EAAE;;QACxG,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC3C,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QAClF,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,CAAC,gBAAgB,GAAG,UAAU,CAAA;QACvC,CAAC;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,CAAA;QAEtE,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM,EAAE,CAAC;YACpD,MAAM,sBAAsB,GAAG,CAAA,MAAA,OAAO,CAAC,OAAO,CAAC,eAAe,0CAAE,eAAe,KAAI,+BAA+B,CAAA;YAClH,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,IAAI,CACP,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAC1D,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,OAAO,CAAC,eAAe,0CAAE,sBAAsB,KAAI,+BAA+B,CAAC,CAAC,CAAC,sBAAsB,CAClK,CAAA;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAA;YACzC,CAAC;QACH,CAAC;QAED,MAAM,eAAe,GAAI,OAAO,CAAC,OAA+B,CAAC,IAAI,CAAA;QACrE,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;YAC5B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAmC,CAAA;YAC3D,MAAM,WAAW,GAAG,OAAO,CAAC,UAAU,CAAA;YACtC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,GAAG,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,wBAAwB,4BAA4B,CAAC,CAAA;YACpH,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;YAC9B,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBAChC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAClB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;YACnD,IAAI,aAAa,KAAK,MAAM,IAAI,aAAa,KAAK,MAAM,EAAE,CAAC;gBACzD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAA;YACnE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,2CAA2C,eAAe,iBAAiB,CAAC,CAAA;YAC9F,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;YAC7C,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM,EAAE,CAAC;gBAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;YAC5B,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QAC9C,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QAC/C,CAAC;QAED,oCAAoC;QACpC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QACpC,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAE,OAAO,CAAC,OAA+B,CAAC,QAAQ,CAAA;QACnG,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;QAC7C,CAAC;QAED,MAAM,cAAc,GAAG,MAAA,OAAO,CAAC,OAAO,CAAC,eAAe,0CAAE,yBAAyB,CAAA;QACjF,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAA;QAC/D,CAAC;QAED,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAA;QACjD,IAAI,CAAC,KAAK,IAAI,iBAAiB,IAAI,IAAI,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;YACpE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAA;QACpC,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACV,2FAA2F;YAC3F,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACnB,wBAAwB;YACxB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACtB,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,aAAa,CAAC,SAAiB,EAAE,IAAY;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,WAAW,IAAI,GAAG,SAAS,EAAE,CAAC,CAAA;IAChH,CAAC;IAED,cAAc,CAAC,UAAkB;QAC/B,wEAAwE;QACxE,IAAI,SAAS,EAAE,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,CAAC,CAAA;QAC3D,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;QAC1E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO;QACpD,IAAI,IAAA,2BAAmB,GAAE,EAAE,CAAC;YAC1B,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,CAAA;QACjC,CAAC;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAA;QACxC,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAA;QACzB,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,iBAAiB,EAAE,CAAA;QAC5C,IAAI,KAAK,EAAE,CAAC;YACV,wEAAwE;YACxE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAA;QAClD,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;YACpE,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC;gBAC5C,GAAG,EAAE,IAAA,4BAAc,EAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;aACrD,CAAA;QACH,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,CAAA;QAC1E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,OAA6B,EAAE,EAAa;;QAC5E,MAAM,sBAAsB,GAAG,MAAA,OAAO,CAAC,eAAe,0CAAE,sBAAsB,CAAA;QAC9E,MAAM,eAAe,GAAG,MAAA,MAAA,OAAO,CAAC,eAAe,0CAAE,eAAe,0CAAE,WAAW,EAAE,CAAA;QAE/E,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAA;QAC3C,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE;YAClC,YAAY;YACZ,iBAAiB;YACjB,UAAU;YACV,oIAAoI;SACrI,CAAC,CAAA;QACF,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAA,sBAAO,EAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAA;QACvF,KAAK,MAAM,QAAQ,IAAI,QAAQ,EAAE,CAAC;YAChC,IACE,CAAC,sBAAsB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;gBACtF,CAAC,eAAe,IAAI,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,EAClF,CAAC;gBACD,SAAQ;YACV,CAAC;YAED,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAA;YACxC,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;YACpE,kBAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,+BAA+B,CAAC,CAAA;YAC/E,oEAAoE;YACpE,MAAM,mBAAmB,GAAG,UAAU,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAA;YAC5E,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,yCAAyC,CAAC,CAAA;YAC1D,OAAO;gBACL,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,KAAK;gBACL,mBAAmB;aACpB,CAAA;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,2BAA2B,sBAAsB,IAAI,eAAe,gBAAgB,SAAS,EAAE,CAAC,CAAA;IAClH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,aAAiD,EAAE,QAAqB;QACnF,kEAAkE;QAClE,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAuB,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QACnF,mCAAmC;QACnC,IAAI,IAAmB,CAAA;QACvB,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;QACrB,IAAI,EAAa,CAAA;QACjB,MAAM,eAAe,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,aAAa,CAAC,OAAQ,CAAC,CAAA,CAAC,mDAAmD;QACvJ,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,eAAe,CAAA;QAC7D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;QAC9C,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;QAC1B,IAAI,eAAe,EAAE,CAAC;YACpB,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAA;YAC5B,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;QAC3D,CAAC;aAAM,CAAC;YACN,EAAE,GAAG,IAAI,cAAS,EAAE,CAAA;YACpB,IAAI,GAAG,aAAa,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,QAAQ,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;gBACzB,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAA;YACpB,CAAC;QACH,CAAC;QAED,MAAM,IAAA,oBAAK,EACT,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,EAC3C,CAAC,EACD,KAAK,EACL,KAAK,EACL,CAAC,EACD,CAAC,CAAM,EAAE,EAAE;YACT,IACE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,2CAA2C,CAAC;gBAC/D,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,4DAA4D,CAAC;gBAChF,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,6DAA6D,CAAC,EACjF,CAAC;gBACD,kBAAG,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;gBACjG,OAAO,IAAI,CAAA;YACb,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CACF,CAAA;IACH,CAAC;CACF;AAlZD,wDAkZC;AAED,SAAgB,SAAS;IACvB,MAAM,UAAU,GAAG,EAAE,CAAC,OAAO,EAAE,CAAA;IAC/B,OAAO,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;AACrE,CAAC", "sourcesContent": ["import { asArray, InvalidConfigurationError, log, retry } from \"builder-util\"\nimport { MemoLazy, parseDn } from \"builder-util-runtime\"\nimport { rename } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as os from \"os\"\nimport * as path from \"path\"\nimport { getBin } from \"../binDownload\"\nimport { Target } from \"../core\"\nimport { WindowsConfiguration } from \"../options/winOptions\"\nimport AppXTarget from \"../targets/AppxTarget\"\nimport { executeAppBuilderAsJson } from \"../util/appBuilder\"\nimport { computeToolEnv, ToolInfo } from \"../util/bundledTool\"\nimport { isUseSystemSigncode } from \"../util/flags\"\nimport { resolveFunction } from \"../util/resolve\"\nimport { VmManager } from \"../vm/vm\"\nimport { WinPackager } from \"../winPackager\"\nimport { importCertificate } from \"./codesign\"\nimport { SignManager } from \"./signManager\"\nimport { WindowsSignOptions } from \"./windowsCodeSign\"\n\nexport function getSignVendorPath() {\n  return getBin(\"winCodeSign\")\n}\n\nexport type CustomWindowsSign = (configuration: CustomWindowsSignTaskConfiguration, packager?: WinPackager) => Promise<any>\n\nexport interface WindowsSignToolOptions extends WindowsSignOptions {\n  readonly name: string\n  readonly site: string | null\n}\n\nexport interface FileCodeSigningInfo {\n  readonly file: string\n  readonly password: string | null\n}\n\nexport interface WindowsSignTaskConfiguration extends WindowsSignToolOptions {\n  readonly cscInfo: FileCodeSigningInfo | CertificateFromStoreInfo | null\n\n  // set if output path differs from input (e.g. osslsigncode cannot sign file in-place)\n  resultOutputPath?: string\n\n  hash: string\n  isNest: boolean\n}\n\nexport interface CustomWindowsSignTaskConfiguration extends WindowsSignTaskConfiguration {\n  computeSignToolArgs(isWin: boolean): Array<string>\n}\n\nexport interface CertificateInfo {\n  readonly commonName: string\n  readonly bloodyMicrosoftSubjectDn: string\n}\n\nexport interface CertificateFromStoreInfo {\n  thumbprint: string\n  subject: string\n  store: string\n  isLocalMachineStore: boolean\n}\n\ninterface CertInfo {\n  Subject: string\n  Thumbprint: string\n  PSParentPath: string\n}\n\nexport class WindowsSignToolManager implements SignManager {\n  private readonly platformSpecificBuildOptions: WindowsConfiguration\n\n  constructor(private readonly packager: WinPackager) {\n    this.platformSpecificBuildOptions = packager.platformSpecificBuildOptions\n  }\n\n  readonly computedPublisherName = new Lazy<Array<string> | null>(async () => {\n    const publisherName = this.platformSpecificBuildOptions.signtoolOptions?.publisherName\n    if (publisherName === null) {\n      return null\n    } else if (publisherName != null) {\n      return asArray(publisherName)\n    }\n\n    const certInfo = await this.lazyCertInfo.value\n    return certInfo == null ? null : [certInfo.commonName]\n  })\n\n  readonly lazyCertInfo = new MemoLazy<MemoLazy<WindowsConfiguration, FileCodeSigningInfo | CertificateFromStoreInfo | null>, CertificateInfo | null>(\n    () => this.cscInfo,\n    async csc => {\n      const cscInfo = await csc.value\n      if (cscInfo == null) {\n        return null\n      }\n\n      if (\"subject\" in cscInfo) {\n        const bloodyMicrosoftSubjectDn = cscInfo.subject\n        return {\n          commonName: parseDn(bloodyMicrosoftSubjectDn).get(\"CN\")!,\n          bloodyMicrosoftSubjectDn,\n        }\n      }\n\n      const cscFile = cscInfo.file\n      if (cscFile == null) {\n        return null\n      }\n      return await this.getCertInfo(cscFile, cscInfo.password || \"\")\n    }\n  )\n\n  readonly cscInfo = new MemoLazy<WindowsConfiguration, FileCodeSigningInfo | CertificateFromStoreInfo | null>(\n    () => this.platformSpecificBuildOptions,\n    platformSpecificBuildOptions => {\n      const subjectName = platformSpecificBuildOptions.signtoolOptions?.certificateSubjectName\n      const shaType = platformSpecificBuildOptions.signtoolOptions?.certificateSha1\n      if (subjectName != null || shaType != null) {\n        return this.packager.vm.value\n          .then(vm => this.getCertificateFromStoreInfo(platformSpecificBuildOptions, vm))\n          .catch((e: any) => {\n            // https://github.com/electron-userland/electron-builder/pull/2397\n            if (platformSpecificBuildOptions.signtoolOptions?.sign == null) {\n              throw e\n            } else {\n              log.debug({ error: e }, \"getCertificateFromStoreInfo error\")\n              return null\n            }\n          })\n      }\n\n      const certificateFile = platformSpecificBuildOptions.signtoolOptions?.certificateFile\n      if (certificateFile != null) {\n        const certificatePassword = this.packager.getCscPassword()\n        return Promise.resolve({\n          file: certificateFile,\n          password: certificatePassword == null ? null : certificatePassword.trim(),\n        })\n      }\n\n      const cscLink = this.packager.getCscLink(\"WIN_CSC_LINK\")\n      if (cscLink == null || cscLink === \"\") {\n        return Promise.resolve(null)\n      }\n\n      return (\n        importCertificate(cscLink, this.packager.info.tempDirManager, this.packager.projectDir)\n          // before then\n          .catch((e: any) => {\n            if (e instanceof InvalidConfigurationError) {\n              throw new InvalidConfigurationError(`Env WIN_CSC_LINK is not correct, cannot resolve: ${e.message}`)\n            } else {\n              throw e\n            }\n          })\n          .then(path => {\n            return {\n              file: path,\n              password: this.packager.getCscPassword(),\n            }\n          })\n      )\n    }\n  )\n\n  initialize(): Promise<void> {\n    return Promise.resolve()\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/2108#issuecomment-333200711\n  async computePublisherName(target: Target, publisherName: string) {\n    if (target instanceof AppXTarget && (await this.cscInfo.value) == null) {\n      log.info({ reason: \"Windows Store only build\" }, \"AppX is not signed\")\n      return publisherName || \"CN=ms\"\n    }\n\n    const certInfo = await this.lazyCertInfo.value\n    const publisher = publisherName || (certInfo == null ? null : certInfo.bloodyMicrosoftSubjectDn)\n    if (publisher == null) {\n      throw new Error(\"Internal error: cannot compute subject using certificate info\")\n    }\n    return publisher\n  }\n\n  async signFile(options: WindowsSignOptions): Promise<boolean> {\n    let hashes = options.options.signtoolOptions?.signingHashAlgorithms\n    // msi does not support dual-signing\n    if (options.path.endsWith(\".msi\")) {\n      hashes = [hashes != null && !hashes.includes(\"sha1\") ? \"sha256\" : \"sha1\"]\n    } else if (options.path.endsWith(\".appx\")) {\n      hashes = [\"sha256\"]\n    } else if (hashes == null) {\n      hashes = [\"sha1\", \"sha256\"]\n    } else {\n      hashes = Array.isArray(hashes) ? hashes : [hashes]\n    }\n\n    const name = this.packager.appInfo.productName\n    const site = await this.packager.appInfo.computePackageUrl()\n\n    const customSign = await resolveFunction(this.packager.appInfo.type, options.options.signtoolOptions?.sign, \"sign\")\n\n    const cscInfo = await this.cscInfo.value\n    if (cscInfo) {\n      let logInfo: any = {\n        file: log.filePath(options.path),\n      }\n      if (\"file\" in cscInfo) {\n        logInfo = {\n          ...logInfo,\n          certificateFile: cscInfo.file,\n        }\n      } else {\n        logInfo = {\n          ...logInfo,\n          subject: cscInfo.subject,\n          thumbprint: cscInfo.thumbprint,\n          store: cscInfo.store,\n          user: cscInfo.isLocalMachineStore ? \"local machine\" : \"current user\",\n        }\n      }\n      log.info(logInfo, \"signing\")\n    } else if (!customSign) {\n      log.debug({ signHook: !!customSign, cscInfo }, \"no signing info identified, signing is skipped\")\n      return false\n    }\n\n    const executor = customSign || ((config: CustomWindowsSignTaskConfiguration, packager: WinPackager) => this.doSign(config, packager))\n    let isNest = false\n    for (const hash of hashes) {\n      const taskConfiguration: WindowsSignTaskConfiguration = { ...options, name, site, cscInfo, hash, isNest }\n      await Promise.resolve(\n        executor(\n          {\n            ...taskConfiguration,\n            computeSignToolArgs: isWin => this.computeSignToolArgs(taskConfiguration, isWin),\n          },\n          this.packager\n        )\n      )\n      isNest = true\n      if (taskConfiguration.resultOutputPath != null) {\n        await rename(taskConfiguration.resultOutputPath, options.path)\n      }\n    }\n\n    return true\n  }\n\n  async getCertInfo(file: string, password: string): Promise<CertificateInfo> {\n    let result: any = null\n    const errorMessagePrefix = \"Cannot extract publisher name from code signing certificate. As workaround, set win.publisherName. Error: \"\n    try {\n      result = await executeAppBuilderAsJson<any>([\"certificate-info\", \"--input\", file, \"--password\", password])\n    } catch (e: any) {\n      throw new Error(`${errorMessagePrefix}${e.stack || e}`)\n    }\n\n    if (result.error != null) {\n      // noinspection ExceptionCaughtLocallyJS\n      throw new InvalidConfigurationError(`${errorMessagePrefix}${result.error}`)\n    }\n    return result\n  }\n\n  // on windows be aware of http://stackoverflow.com/a/32640183/1910191\n  computeSignToolArgs(options: WindowsSignTaskConfiguration, isWin: boolean, vm: VmManager = new VmManager()): Array<string> {\n    const inputFile = vm.toVmFile(options.path)\n    const outputPath = isWin ? inputFile : this.getOutputPath(inputFile, options.hash)\n    if (!isWin) {\n      options.resultOutputPath = outputPath\n    }\n\n    const args = isWin ? [\"sign\"] : [\"-in\", inputFile, \"-out\", outputPath]\n\n    if (process.env.ELECTRON_BUILDER_OFFLINE !== \"true\") {\n      const timestampingServiceUrl = options.options.signtoolOptions?.timeStampServer || \"http://timestamp.digicert.com\"\n      if (isWin) {\n        args.push(\n          options.isNest || options.hash === \"sha256\" ? \"/tr\" : \"/t\",\n          options.isNest || options.hash === \"sha256\" ? options.options.signtoolOptions?.rfc3161TimeStampServer || \"http://timestamp.digicert.com\" : timestampingServiceUrl\n        )\n      } else {\n        args.push(\"-t\", timestampingServiceUrl)\n      }\n    }\n\n    const certificateFile = (options.cscInfo as FileCodeSigningInfo).file\n    if (certificateFile == null) {\n      const cscInfo = options.cscInfo as CertificateFromStoreInfo\n      const subjectName = cscInfo.thumbprint\n      if (!isWin) {\n        throw new Error(`${subjectName == null ? \"certificateSha1\" : \"certificateSubjectName\"} supported only on Windows`)\n      }\n\n      args.push(\"/sha1\", cscInfo.thumbprint)\n      args.push(\"/s\", cscInfo.store)\n      if (cscInfo.isLocalMachineStore) {\n        args.push(\"/sm\")\n      }\n    } else {\n      const certExtension = path.extname(certificateFile)\n      if (certExtension === \".p12\" || certExtension === \".pfx\") {\n        args.push(isWin ? \"/f\" : \"-pkcs12\", vm.toVmFile(certificateFile))\n      } else {\n        throw new Error(`Please specify pkcs12 (.p12/.pfx) file, ${certificateFile} is not correct`)\n      }\n    }\n\n    if (!isWin || options.hash !== \"sha1\") {\n      args.push(isWin ? \"/fd\" : \"-h\", options.hash)\n      if (isWin && process.env.ELECTRON_BUILDER_OFFLINE !== \"true\") {\n        args.push(\"/td\", \"sha256\")\n      }\n    }\n\n    if (options.name) {\n      args.push(isWin ? \"/d\" : \"-n\", options.name)\n    }\n\n    if (options.site) {\n      args.push(isWin ? \"/du\" : \"-i\", options.site)\n    }\n\n    // msi does not support dual-signing\n    if (options.isNest) {\n      args.push(isWin ? \"/as\" : \"-nest\")\n    }\n\n    const password = options.cscInfo == null ? null : (options.cscInfo as FileCodeSigningInfo).password\n    if (password) {\n      args.push(isWin ? \"/p\" : \"-pass\", password)\n    }\n\n    const additionalCert = options.options.signtoolOptions?.additionalCertificateFile\n    if (additionalCert) {\n      args.push(isWin ? \"/ac\" : \"-ac\", vm.toVmFile(additionalCert))\n    }\n\n    const httpsProxyFromEnv = process.env.HTTPS_PROXY\n    if (!isWin && httpsProxyFromEnv != null && httpsProxyFromEnv.length) {\n      args.push(\"-p\", httpsProxyFromEnv)\n    }\n\n    if (isWin) {\n      // https://github.com/electron-userland/electron-builder/issues/2875#issuecomment-387233610\n      args.push(\"/debug\")\n      // must be last argument\n      args.push(inputFile)\n    }\n\n    return args\n  }\n\n  getOutputPath(inputPath: string, hash: string) {\n    const extension = path.extname(inputPath)\n    return path.join(path.dirname(inputPath), `${path.basename(inputPath, extension)}-signed-${hash}${extension}`)\n  }\n\n  getWinSignTool(vendorPath: string): string {\n    // use modern signtool on Windows Server 2012 R2 to be able to sign AppX\n    if (isOldWin6()) {\n      return path.join(vendorPath, \"windows-6\", \"signtool.exe\")\n    } else {\n      return path.join(vendorPath, \"windows-10\", process.arch, \"signtool.exe\")\n    }\n  }\n\n  async getToolPath(isWin = process.platform === \"win32\"): Promise<ToolInfo> {\n    if (isUseSystemSigncode()) {\n      return { path: \"osslsigncode\" }\n    }\n\n    const result = process.env.SIGNTOOL_PATH\n    if (result) {\n      return { path: result }\n    }\n\n    const vendorPath = await getSignVendorPath()\n    if (isWin) {\n      // use modern signtool on Windows Server 2012 R2 to be able to sign AppX\n      return { path: this.getWinSignTool(vendorPath) }\n    } else if (process.platform === \"darwin\") {\n      const toolDirPath = path.join(vendorPath, process.platform, \"10.12\")\n      return {\n        path: path.join(toolDirPath, \"osslsigncode\"),\n        env: computeToolEnv([path.join(toolDirPath, \"lib\")]),\n      }\n    } else {\n      return { path: path.join(vendorPath, process.platform, \"osslsigncode\") }\n    }\n  }\n\n  async getCertificateFromStoreInfo(options: WindowsConfiguration, vm: VmManager): Promise<CertificateFromStoreInfo> {\n    const certificateSubjectName = options.signtoolOptions?.certificateSubjectName\n    const certificateSha1 = options.signtoolOptions?.certificateSha1?.toUpperCase()\n\n    const ps = await vm.powershellCommand.value\n    const rawResult = await vm.exec(ps, [\n      \"-NoProfile\",\n      \"-NonInteractive\",\n      \"-Command\",\n      \"Get-ChildItem -Recurse Cert: -CodeSigningCert | Select-Object -Property Subject,PSParentPath,Thumbprint | ConvertTo-Json -Compress\",\n    ])\n    const certList = rawResult.length === 0 ? [] : asArray<CertInfo>(JSON.parse(rawResult))\n    for (const certInfo of certList) {\n      if (\n        (certificateSubjectName != null && !certInfo.Subject.includes(certificateSubjectName)) ||\n        (certificateSha1 != null && certInfo.Thumbprint.toUpperCase() !== certificateSha1)\n      ) {\n        continue\n      }\n\n      const parentPath = certInfo.PSParentPath\n      const store = parentPath.substring(parentPath.lastIndexOf(\"\\\\\") + 1)\n      log.debug({ store, PSParentPath: parentPath }, \"auto-detect certificate store\")\n      // https://github.com/electron-userland/electron-builder/issues/1717\n      const isLocalMachineStore = parentPath.includes(\"Certificate::LocalMachine\")\n      log.debug(null, \"auto-detect using of LocalMachine store\")\n      return {\n        thumbprint: certInfo.Thumbprint,\n        subject: certInfo.Subject,\n        store,\n        isLocalMachineStore,\n      }\n    }\n\n    throw new Error(`Cannot find certificate ${certificateSubjectName || certificateSha1}, all certs: ${rawResult}`)\n  }\n\n  async doSign(configuration: CustomWindowsSignTaskConfiguration, packager: WinPackager) {\n    // https://github.com/electron-userland/electron-builder/pull/1944\n    const timeout = parseInt(process.env.SIGNTOOL_TIMEOUT as any, 10) || 10 * 60 * 1000\n    // decide runtime argument by cases\n    let args: Array<string>\n    let env = process.env\n    let vm: VmManager\n    const useVmIfNotOnWin = configuration.path.endsWith(\".appx\") || !(\"file\" in configuration.cscInfo!) /* certificateSubjectName and other such options */\n    const isWin = process.platform === \"win32\" || useVmIfNotOnWin\n    const toolInfo = await this.getToolPath(isWin)\n    const tool = toolInfo.path\n    if (useVmIfNotOnWin) {\n      vm = await packager.vm.value\n      args = this.computeSignToolArgs(configuration, isWin, vm)\n    } else {\n      vm = new VmManager()\n      args = configuration.computeSignToolArgs(isWin)\n      if (toolInfo.env != null) {\n        env = toolInfo.env\n      }\n    }\n\n    await retry(\n      () => vm.exec(tool, args, { timeout, env }),\n      2,\n      15000,\n      10000,\n      0,\n      (e: any) => {\n        if (\n          e.message.includes(\"The file is being used by another process\") ||\n          e.message.includes(\"The specified timestamp server either could not be reached\") ||\n          e.message.includes(\"No certificates were found that met all the given criteria.\")\n        ) {\n          log.warn(`Attempt to code sign failed, another attempt will be made in 15 seconds: ${e.message}`)\n          return true\n        }\n        return false\n      }\n    )\n  }\n}\n\nexport function isOldWin6() {\n  const winVersion = os.release()\n  return winVersion.startsWith(\"6.\") && !winVersion.startsWith(\"6.3\")\n}\n"]}