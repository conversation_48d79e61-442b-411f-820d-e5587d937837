{"name": "app-builder-lib", "description": "electron-builder lib", "version": "26.0.12", "main": "out/index.js", "files": ["out", "templates", "scheme.json", "certs/root_certs.keychain"], "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/app-builder-lib"}, "engines": {"node": ">=14.0.0"}, "keywords": ["electron", "builder", "build", "installer", "install", "packager", "pack", "nsis", "app", "dmg", "pkg", "msi", "exe", "setup", "Windows", "OS X", "MacOS", "<PERSON>", "appx", "snap", "flatpak", "portable"], "author": "<PERSON>", "license": "MIT", "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "dependencies": {"@develar/schema-utils": "~2.6.5", "@electron/asar": "3.2.18", "@electron/fuses": "^1.8.0", "@electron/notarize": "2.5.0", "@electron/osx-sign": "1.3.1", "@electron/rebuild": "3.7.0", "@electron/universal": "2.0.1", "@malept/flatpak-bundler": "^0.4.0", "@types/fs-extra": "9.0.13", "async-exit-hook": "^2.0.1", "chromium-pickle-js": "^0.2.0", "config-file-ts": "0.2.8-rc1", "debug": "^4.3.4", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "ejs": "^3.1.8", "fs-extra": "^10.1.0", "hosted-git-info": "^4.1.0", "is-ci": "^3.0.0", "isbinaryfile": "^5.0.0", "js-yaml": "^4.1.0", "json5": "^2.2.3", "lazy-val": "^1.0.5", "minimatch": "^10.0.0", "resedit": "^1.7.0", "semver": "^7.3.8", "tar": "^6.1.12", "temp-file": "^3.4.0", "tiny-async-pool": "1.3.0", "plist": "3.1.0", "builder-util": "26.0.11", "electron-publish": "26.0.11", "builder-util-runtime": "9.3.1"}, "///": "babel in devDependencies for proton tests", "devDependencies": {"@babel/core": "7.24.9", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.24.7", "@babel/plugin-proposal-do-expressions": "7.24.7", "@babel/plugin-proposal-export-default-from": "7.24.7", "@babel/plugin-proposal-export-namespace-from": "7.18.9", "@babel/plugin-proposal-function-bind": "7.24.7", "@babel/plugin-proposal-function-sent": "7.24.7", "@babel/plugin-proposal-json-strings": "7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-numeric-separator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-pipeline-operator": "7.24.7", "@babel/plugin-proposal-throw-expressions": "7.24.7", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-import-meta": "7.10.4", "@babel/preset-env": "7.24.8", "@babel/preset-react": "7.24.7", "@types/debug": "4.1.7", "@types/ejs": "3.1.0", "@types/hosted-git-info": "3.0.2", "@types/is-ci": "3.0.0", "@types/js-yaml": "4.0.3", "@types/semver": "7.3.8", "@types/tar": "^6.1.3", "@types/tiny-async-pool": "^1", "@types/plist": "3.0.5", "toml": "^3.0.0", "dmg-builder": "26.0.12", "electron-builder-squirrel-windows": "26.0.12"}, "peerDependencies": {"dmg-builder": "26.0.12", "electron-builder-squirrel-windows": "26.0.12"}, "//": "electron-builder-squirrel-windows and dmg-builder added as dev dep for tests (as otherwise `require` doesn't work using Yarn 2)", "typings": "./out/index.d.ts"}