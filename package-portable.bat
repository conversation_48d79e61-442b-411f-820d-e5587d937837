@echo off
echo ========================================
echo    Hookah Ads - Creating Portable Package
echo ========================================
echo.

echo Checking if build exists...
if not exist "dist\win-unpacked\Hookah Ads.exe" (
    echo ERROR: Built application not found!
    echo Please run "npm run build-portable" first.
    pause
    exit /b 1
)

echo.
echo Creating portable package...
if exist "HookahAds-Portable-Fixed" rmdir /s /q "HookahAds-Portable-Fixed"
mkdir "HookahAds-Portable-Fixed"

echo.
echo Copying application files...
xcopy "dist\win-unpacked\*" "HookahAds-Portable-Fixed\" /E /I /Q

echo.
echo Creating documentation...
copy "README.md" "HookahAds-Portable-Fixed\"
copy "DEPLOYMENT.md" "HookahAds-Portable-Fixed\"

echo.
echo Creating startup instructions...
echo HOOKAH ADS - PORTABLE VERSION - FIXED SCROLLING > "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo. >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo To run the application: >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo 1. Double-click "Hookah Ads.exe" >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo. >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo FIXED: Text now starts from the right side of screen! >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo. >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo Features: >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo - Banner window appears at bottom of screen >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo - Control panel opens for managing ads >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo - Add text and images to your advertisements >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo - Customize colors, height, and scroll speed >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo - Text scrolls properly from right to left >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo. >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo No installation required! >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"
echo Just copy this folder to any Windows computer and run. >> "HookahAds-Portable-Fixed\HOW-TO-RUN.txt"

echo.
echo Creating ZIP archive...
if exist "HookahAds-Portable-Fixed.zip" del "HookahAds-Portable-Fixed.zip"
powershell -command "Compress-Archive -Path 'HookahAds-Portable-Fixed' -DestinationPath 'HookahAds-Portable-Fixed.zip'"

echo.
echo ========================================
echo     FIXED PORTABLE PACKAGE READY!
echo ========================================
echo.
echo Files created:
echo - HookahAds-Portable-Fixed\ (folder - %~dp0HookahAds-Portable-Fixed)
echo - HookahAds-Portable-Fixed.zip (archive - %~dp0HookahAds-Portable-Fixed.zip)
echo.
echo FIXED: Text now scrolls properly from right to left!
echo.
echo To deploy to another computer:
echo 1. Copy the "HookahAds-Portable-Fixed" folder OR
echo 2. Copy and extract "HookahAds-Portable-Fixed.zip"
echo 3. Double-click "Hookah Ads.exe" to run
echo.
echo No additional software installation required!
echo.
pause
