@echo off
echo ========================================
echo    Hookah Ads - Creating Portable Package
echo ========================================
echo.

echo Checking if build exists...
if not exist "dist\win-unpacked\Hookah Ads.exe" (
    echo ERROR: Built application not found!
    echo Please run "npm run build-portable" first.
    pause
    exit /b 1
)

echo.
echo Creating portable package...
if exist "HookahAds-Portable" rmdir /s /q "HookahAds-Portable"
mkdir "HookahAds-Portable"

echo.
echo Copying application files...
xcopy "dist\win-unpacked\*" "HookahAds-Portable\" /E /I /Q

echo.
echo Creating documentation...
copy "README.md" "HookahAds-Portable\"
copy "DEPLOYMENT.md" "HookahAds-Portable\"

echo.
echo Creating startup instructions...
echo HOOKAH ADS - PORTABLE VERSION > "HookahAds-Portable\HOW-TO-RUN.txt"
echo. >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo To run the application: >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo 1. Double-click "Hookah Ads.exe" >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo. >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo Features: >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo - Banner window appears at bottom of screen >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo - Control panel opens for managing ads >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo - Add text and images to your advertisements >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo - Customize colors, height, and scroll speed >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo. >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo No installation required! >> "HookahAds-Portable\HOW-TO-RUN.txt"
echo Just copy this folder to any Windows computer and run. >> "HookahAds-Portable\HOW-TO-RUN.txt"

echo.
echo Creating ZIP archive...
if exist "HookahAds-Portable.zip" del "HookahAds-Portable.zip"
powershell -command "Compress-Archive -Path 'HookahAds-Portable' -DestinationPath 'HookahAds-Portable.zip'"

echo.
echo ========================================
echo        PORTABLE PACKAGE READY!
echo ========================================
echo.
echo Files created:
echo - HookahAds-Portable\ (folder - %~dp0HookahAds-Portable)
echo - HookahAds-Portable.zip (archive - %~dp0HookahAds-Portable.zip)
echo.
echo To deploy to another computer:
echo 1. Copy the "HookahAds-Portable" folder OR
echo 2. Copy and extract "HookahAds-Portable.zip"
echo 3. Double-click "Hookah Ads.exe" to run
echo.
echo No additional software installation required!
echo.
pause
