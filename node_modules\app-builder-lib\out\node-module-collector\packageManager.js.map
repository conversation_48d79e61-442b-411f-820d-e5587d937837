{"version": 3, "file": "packageManager.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/packageManager.ts"], "names": [], "mappings": ";;;AAwFA,4DAEC;AAED,gCAEC;AA9FD,oFAAoF;AACpF,wFAAwF;AACxF,+BAAuC;AACvC,+CAA2C;AAI3C,MAAM,KAAK,GAAG,IAAI,GAAG,EAAE,CAAA;AACvB,MAAM,uBAAuB,GAAG,IAAI,GAAG,EAAmB,CAAA;AAC1D,MAAM,aAAa,GAAG,IAAI,GAAG,EAAc,CAAA;AAE3C;;GAEG;AACH,SAAS,qBAAqB,CAAC,EAAM;IACnC,MAAM,GAAG,GAAG,cAAc,EAAE,EAAE,CAAA;IAC9B,IAAI,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;QACrC,OAAO,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,CAAA;IAC3D,CAAC;IAED,OAAO,IAAA,mBAAI,EAAC,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;SAC5C,IAAI,CAAC,GAAG,CAAC,EAAE;QACV,OAAO,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAClC,CAAC,CAAC;SACD,IAAI,CAAC,KAAK,CAAC,EAAE;QACZ,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACvC,OAAO,KAAK,CAAA;IACd,CAAC,CAAC;SACD,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAA;AACvB,CAAC;AAED,SAAS,iBAAiB,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE;IAC5C,MAAM,GAAG,GAAG,YAAY,GAAG,EAAE,CAAA;IAC7B,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,CAAA;IACjD,CAAC;IAED,OAAO,OAAO,CAAC,GAAG,CAAC;QACjB,IAAA,qBAAM,EAAC,IAAA,cAAO,EAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QACjC,IAAA,qBAAM,EAAC,IAAA,cAAO,EAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;QACzC,IAAA,qBAAM,EAAC,IAAA,cAAO,EAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;QACtC,IAAA,qBAAM,EAAC,IAAA,cAAO,EAAC,GAAG,EAAE,WAAW,CAAC,CAAC;KAClC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE;QACrC,IAAI,KAAS,CAAA;QAEb,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,GAAG,MAAM,CAAA;QAChB,CAAC;aAAM,IAAI,MAAM,EAAE,CAAC;YAClB,KAAK,GAAG,MAAM,CAAA;QAChB,CAAC;aAAM,IAAI,KAAK,EAAE,CAAC;YACjB,KAAK,GAAG,KAAK,CAAA;QACf,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,KAAK,CAAA;QACf,CAAC;QAED,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACrB,OAAO,KAAK,CAAA;IACd,CAAC,CAAC,CAAA;AACJ,CAAC;AAEM,MAAM,MAAM,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,gBAAgB,KAAmD,EAAE,EAAE,EAAE;IAC3G,IAAI,IAAI,GAAG,MAAM,iBAAiB,CAAC,GAAG,CAAC,CAAA;IACvC,IAAI,IAAI,EAAE,CAAC;QACT,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,MAAM,GAAG,GAAG,IAAI,GAAG,CAAA;IACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5B,MAAM,GAAG,IAAA,cAAO,EAAC,MAAM,CAAC,CAAA;QACxB,IAAI,GAAG,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA;QACtC,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,IAAI,MAAM,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,OAAO,MAAM,CAAA;IACf,CAAC;IACD,IAAI,MAAM,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,OAAO,MAAM,CAAA;IACf,CAAC;IAED,IAAI,gBAAgB,IAAI,CAAC,MAAM,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC7D,OAAO,KAAK,CAAA;IACd,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AA1BY,QAAA,MAAM,UA0BlB;AAED,SAAgB,wBAAwB,CAAC,EAAM;IAC7C,OAAO,IAAA,mBAAI,EAAC,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;AACzE,CAAC;AAED,SAAgB,UAAU;IACxB,OAAO,KAAK,CAAC,KAAK,EAAE,CAAA;AACtB,CAAC", "sourcesContent": ["// copy from https://github.com/egoist/detect-package-manager/blob/main/src/index.ts\n// and merge https://github.com/egoist/detect-package-manager/pull/9 to support Monorepo\nimport { resolve, dirname } from \"path\"\nimport { exec, exists } from \"builder-util\"\n\nexport type PM = \"npm\" | \"yarn\" | \"pnpm\" | \"bun\"\n\nconst cache = new Map()\nconst globalInstallationCache = new Map<string, boolean>()\nconst lockfileCache = new Map<string, PM>()\n\n/**\n * Check if a global pm is available\n */\nfunction hasGlobalInstallation(pm: PM): Promise<boolean> {\n  const key = `has_global_${pm}`\n  if (globalInstallationCache.has(key)) {\n    return Promise.resolve(globalInstallationCache.get(key)!)\n  }\n\n  return exec(pm, [\"--version\"], { shell: true })\n    .then(res => {\n      return /^\\d+.\\d+.\\d+$/.test(res)\n    })\n    .then(value => {\n      globalInstallationCache.set(key, value)\n      return value\n    })\n    .catch(() => false)\n}\n\nfunction getTypeofLockFile(cwd = process.cwd()): Promise<PM> {\n  const key = `lockfile_${cwd}`\n  if (lockfileCache.has(key)) {\n    return Promise.resolve(lockfileCache.get(key)!)\n  }\n\n  return Promise.all([\n    exists(resolve(cwd, \"yarn.lock\")),\n    exists(resolve(cwd, \"package-lock.json\")),\n    exists(resolve(cwd, \"pnpm-lock.yaml\")),\n    exists(resolve(cwd, \"bun.lockb\")),\n  ]).then(([isYarn, _, isPnpm, isBun]) => {\n    let value: PM\n\n    if (isYarn) {\n      value = \"yarn\"\n    } else if (isPnpm) {\n      value = \"pnpm\"\n    } else if (isBun) {\n      value = \"bun\"\n    } else {\n      value = \"npm\"\n    }\n\n    cache.set(key, value)\n    return value\n  })\n}\n\nexport const detect = async ({ cwd, includeGlobalBun }: { cwd?: string; includeGlobalBun?: boolean } = {}) => {\n  let type = await getTypeofLockFile(cwd)\n  if (type) {\n    return type\n  }\n\n  let tmpCwd = cwd || \".\"\n  for (let i = 1; i <= 5; i++) {\n    tmpCwd = dirname(tmpCwd)\n    type = await getTypeofLockFile(tmpCwd)\n    if (type) {\n      return type\n    }\n  }\n\n  if (await hasGlobalInstallation(\"yarn\")) {\n    return \"yarn\"\n  }\n  if (await hasGlobalInstallation(\"pnpm\")) {\n    return \"yarn\"\n  }\n\n  if (includeGlobalBun && (await hasGlobalInstallation(\"bun\"))) {\n    return \"bun\"\n  }\n  return \"npm\"\n}\n\nexport function getPackageManagerVersion(pm: PM) {\n  return exec(pm, [\"--version\"], { shell: true }).then(res => res.trim())\n}\n\nexport function clearCache() {\n  return cache.clear()\n}\n"]}