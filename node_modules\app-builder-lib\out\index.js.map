{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAwEA,4DAMC;AAED,sBA2DC;AA3ID,+CAA6E;AAC7E,+DAA8C;AAE9C,yCAAqC;AAErC,6DAAyD;AACzD,4CAAgD;AAEhD,6CAAkE;AAAzD,oGAAA,IAAI,OAAA;AAAE,8GAAA,cAAc,OAAA;AAAE,6GAAA,aAAa,OAAA;AAC5C,qCAAmC;AAA1B,kGAAA,OAAO,OAAA;AAahB,+BAWe;AARb,sGAAA,cAAc,OAAA;AACd,kGAAA,UAAU,OAAA;AACV,gGAAA,QAAQ,OAAA;AAER,8FAAA,MAAM,OAAA;AAmBR,uCAAkD;AAA5B,oGAAA,QAAQ,OAAA;AAI9B,6DAAsE;AAA7D,yHAAA,iBAAiB,OAAA;AAU1B,6CAAwD;AAAjC,yGAAA,UAAU,OAAA;AAEjC,iDAA+C;AAAtC,8GAAA,aAAa,OAAA;AACtB,6CAAgF;AAAlC,0GAAA,WAAW,OAAA;AACzD,uDAAqD;AAA5C,oHAAA,gBAAgB,OAAA;AACzB,2DAAyD;AAAhD,gHAAA,cAAc,OAAA;AACvB,6CAA2C;AAAlC,0GAAA,WAAW,OAAA;AAEpB,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,yBAAyB,EAAE,QAAQ,EAAE,yBAAyB,EAAE,aAAa,CAAC,CAAC,CAAA;AAE3K,SAAgB,wBAAwB,CAAC,OAAyC;IAChF,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,IAAK,OAAe,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE,CAAC;YACnF,MAAM,IAAI,wCAAyB,CAAC,mBAAmB,UAAU,GAAG,CAAC,CAAA;QACvE,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAgB,KAAK,CAAC,OAAyC,EAAE,WAAqB,IAAI,mBAAQ,CAAC,OAAO,CAAC;IACzG,wBAAwB,CAAC,OAAO,CAAC,CAAA;IAEjC,MAAM,cAAc,GAAG,IAAI,+BAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IAC5D,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,kBAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;QAC/B,QAAQ,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAA;QACnC,cAAc,CAAC,WAAW,EAAE,CAAA;IAC9B,CAAC,CAAA;IACD,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;IAErC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,EAAC,WAAW,EAAC,EAAE;QACxD,MAAM,qBAAqB,GAAG,MAAM,IAAA,yBAAe,EAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,aAAa,CAAC,qBAAqB,EAAE,uBAAuB,CAAC,CAAA;QACpJ,IAAI,qBAAqB,IAAI,IAAI,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,IAAA,8BAAO,EAAC,MAAM,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;YACvF,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;gBAC3D,OAAO,WAAW,CAAC,aAAa,CAAA;YAClC,CAAC;YAED,MAAM,qBAAqB,GAAG,MAAM,cAAc,CAAC,8BAA8B,EAAE,CAAA;YACnF,IAAI,qBAAqB,IAAI,IAAI,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxE,OAAO,WAAW,CAAC,aAAa,CAAA;YAClC,CAAC;YAED,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;gBACvC,IAAI,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;oBACpD,kBAAG,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,EAAE,iDAAiD,CAAC,CAAA;oBAC5E,SAAQ;gBACV,CAAC;gBACD,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAC3C,KAAK,MAAM,oBAAoB,IAAI,qBAAqB,EAAE,CAAC;oBACzD,MAAM,cAAc,CAAC,cAAc,CACjC,oBAAoB,EACpB;wBACE,IAAI,EAAE,WAAW;wBACjB,IAAI,EAAE,IAAI;qBACX,EACD,QAAQ,CAAC,OAAO,CACjB,CAAA;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,WAAW,CAAC,aAAa,CAAA;IAClC,CAAC,CAAC,CAAA;IAEF,OAAO,IAAA,6BAAc,EAAC,OAAO,EAAE,eAAe,CAAC,EAAE;QAC/C,IAAI,OAAqB,CAAA;QACzB,IAAI,eAAe,EAAE,CAAC;YACpB,cAAc,CAAC,WAAW,EAAE,CAAA;YAC5B,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACjC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAA;QACvC,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;YACvB,QAAQ,CAAC,2BAA2B,EAAE,CAAA;YACtC,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC", "sourcesContent": ["import { InvalidConfigurationError, executeFinally, log } from \"builder-util\"\nimport { asArray } from \"builder-util-runtime\"\nimport { PublishOptions } from \"electron-publish\"\nimport { Packager } from \"./packager\"\nimport { PackagerOptions } from \"./packagerApi\"\nimport { PublishManager } from \"./publish/PublishManager\"\nimport { resolveFunction } from \"./util/resolve\"\n\nexport { Arch, archFromString, getArchSuffix } from \"builder-util\"\nexport { AppInfo } from \"./appInfo\"\nexport {\n  AfterExtractContext,\n  AfterPackContext,\n  BeforePackContext,\n  CommonConfiguration,\n  Configuration,\n  FuseOptionsV1,\n  Hook,\n  Hooks,\n  MetadataDirectories,\n  PackContext,\n} from \"./configuration\"\nexport {\n  BeforeBuildContext,\n  CompressionLevel,\n  DEFAULT_TARGET,\n  DIR_TARGET,\n  Platform,\n  SourceRepositoryInfo,\n  Target,\n  TargetConfigType,\n  TargetConfiguration,\n  TargetSpecificOptions,\n} from \"./core\"\nexport { ElectronBrandingOptions, ElectronDownloadOptions, ElectronPlatformName } from \"./electron/ElectronFramework\"\nexport { AppXOptions } from \"./options/AppXOptions\"\nexport { CommonWindowsInstallerConfiguration } from \"./options/CommonWindowsInstallerConfiguration\"\nexport { FileAssociation } from \"./options/FileAssociation\"\nexport { AppImageOptions, CommonLinuxOptions, DebOptions, FlatpakOptions, LinuxConfiguration, LinuxDesktopFile, LinuxTargetSpecificOptions } from \"./options/linuxOptions\"\nexport { DmgContent, DmgOptions, DmgWindow, MacConfiguration, MacOsTargetName, MasConfiguration } from \"./options/macOptions\"\nexport { AuthorMetadata, Metadata, RepositoryInfo } from \"./options/metadata\"\nexport { MsiOptions } from \"./options/MsiOptions\"\nexport { MsiWrappedOptions } from \"./options/MsiWrappedOptions\"\nexport { BackgroundAlignment, BackgroundScaling, PkgBackgroundOptions, PkgOptions } from \"./options/pkgOptions\"\nexport { AsarOptions, FileSet, FilesBuildOptions, PlatformSpecificBuildOptions, Protocol, ReleaseInfo } from \"./options/PlatformSpecificBuildOptions\"\nexport { PlugDescriptor, SlotDescriptor, SnapOptions } from \"./options/SnapOptions\"\nexport { SquirrelWindowsOptions } from \"./options/SquirrelWindowsOptions\"\nexport { WindowsAzureSigningConfiguration, WindowsConfiguration, WindowsSigntoolConfiguration } from \"./options/winOptions\"\nexport { BuildResult, Packager } from \"./packager\"\nexport { ArtifactBuildStarted, ArtifactCreated, PackagerOptions } from \"./packagerApi\"\nexport { CommonNsisOptions, CustomNsisBinary, NsisOptions, NsisWebOptions, PortableOptions } from \"./targets/nsis/nsisOptions\"\n\nexport { CancellationToken, ProgressInfo } from \"builder-util-runtime\"\nexport { PublishOptions, UploadTask } from \"electron-publish\"\nexport { WindowsSignOptions } from \"./codeSign/windowsCodeSign\"\nexport {\n  CertificateFromStoreInfo,\n  CustomWindowsSign,\n  CustomWindowsSignTaskConfiguration,\n  FileCodeSigningInfo,\n  WindowsSignTaskConfiguration,\n} from \"./codeSign/windowsSignToolManager\"\nexport { ForgeOptions, buildForge } from \"./forge-maker\"\nexport { Framework, PrepareApplicationStageDirectoryOptions } from \"./Framework\"\nexport { LinuxPackager } from \"./linuxPackager\"\nexport { CustomMacSign, CustomMacSignOptions, MacPackager } from \"./macPackager\"\nexport { PlatformPackager } from \"./platformPackager\"\nexport { PublishManager } from \"./publish/PublishManager\"\nexport { WinPackager } from \"./winPackager\"\n\nconst expectedOptions = new Set([\"publish\", \"targets\", \"mac\", \"win\", \"linux\", \"projectDir\", \"platformPackagerFactory\", \"config\", \"effectiveOptionComputed\", \"prepackaged\"])\n\nexport function checkBuildRequestOptions(options: PackagerOptions & PublishOptions) {\n  for (const optionName of Object.keys(options)) {\n    if (!expectedOptions.has(optionName) && (options as any)[optionName] !== undefined) {\n      throw new InvalidConfigurationError(`Unknown option \"${optionName}\"`)\n    }\n  }\n}\n\nexport function build(options: PackagerOptions & PublishOptions, packager: Packager = new Packager(options)): Promise<Array<string>> {\n  checkBuildRequestOptions(options)\n\n  const publishManager = new PublishManager(packager, options)\n  const sigIntHandler = () => {\n    log.warn(\"cancelled by SIGINT\")\n    packager.cancellationToken.cancel()\n    publishManager.cancelTasks()\n  }\n  process.once(\"SIGINT\", sigIntHandler)\n\n  const promise = packager.build().then(async buildResult => {\n    const afterAllArtifactBuild = await resolveFunction(packager.appInfo.type, buildResult.configuration.afterAllArtifactBuild, \"afterAllArtifactBuild\")\n    if (afterAllArtifactBuild != null) {\n      const newArtifacts = asArray(await Promise.resolve(afterAllArtifactBuild(buildResult)))\n      if (newArtifacts.length === 0 || !publishManager.isPublish) {\n        return buildResult.artifactPaths\n      }\n\n      const publishConfigurations = await publishManager.getGlobalPublishConfigurations()\n      if (publishConfigurations == null || publishConfigurations.length === 0) {\n        return buildResult.artifactPaths\n      }\n\n      for (const newArtifact of newArtifacts) {\n        if (buildResult.artifactPaths.includes(newArtifact)) {\n          log.warn({ newArtifact }, \"skipping publish of artifact, already published\")\n          continue\n        }\n        buildResult.artifactPaths.push(newArtifact)\n        for (const publishConfiguration of publishConfigurations) {\n          await publishManager.scheduleUpload(\n            publishConfiguration,\n            {\n              file: newArtifact,\n              arch: null,\n            },\n            packager.appInfo\n          )\n        }\n      }\n    }\n    return buildResult.artifactPaths\n  })\n\n  return executeFinally(promise, isErrorOccurred => {\n    let promise: Promise<any>\n    if (isErrorOccurred) {\n      publishManager.cancelTasks()\n      promise = Promise.resolve(null)\n    } else {\n      promise = publishManager.awaitTasks()\n    }\n\n    return promise.then(() => {\n      packager.clearPackagerEventListeners()\n      process.removeListener(\"SIGINT\", sigIntHandler)\n    })\n  })\n}\n"]}