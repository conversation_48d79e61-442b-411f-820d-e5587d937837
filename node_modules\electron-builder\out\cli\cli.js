#! /usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electronVersion_1 = require("app-builder-lib/out/electron/electronVersion");
const load_1 = require("app-builder-lib/out/util/config/load");
const yarn_1 = require("app-builder-lib/out/util/yarn");
const builder_util_1 = require("builder-util");
const chalk = require("chalk");
const fs_extra_1 = require("fs-extra");
const isCi = require("is-ci");
const path = require("path");
const builder_1 = require("../builder");
const publish_1 = require("../publish");
const create_self_signed_cert_1 = require("./create-self-signed-cert");
const install_app_deps_1 = require("./install-app-deps");
const start_1 = require("./start");
// tslint:disable:no-unused-expression
void (0, builder_1.createYargs)()
    .command(["build", "*"], "Build", builder_1.configureBuildCommand, wrap(builder_1.build))
    .command("install-app-deps", "Install app deps", install_app_deps_1.configureInstallAppDepsCommand, wrap(install_app_deps_1.installAppDeps))
    .command("node-gyp-rebuild", "Rebuild own native code", install_app_deps_1.configureInstallAppDepsCommand /* yes, args the same as for install app deps */, wrap(rebuildAppNativeCode))
    .command("publish", "Publish a list of artifacts", publish_1.configurePublishCommand, wrap(publish_1.publish))
    .command("create-self-signed-cert", "Create self-signed code signing cert for Windows apps", yargs => yargs
    .option("publisher", {
    alias: ["p"],
    type: "string",
    requiresArg: true,
    description: "The publisher name",
})
    .demandOption("publisher"), wrap(argv => (0, create_self_signed_cert_1.createSelfSignedCert)(argv.publisher)))
    .command("start", "Run application in a development mode using electron-webpack", yargs => yargs, wrap(() => (0, start_1.start)()))
    .help()
    .epilog(`See ${chalk.underline("https://electron.build")} for more documentation.`)
    .strict()
    .recommendCommands().argv;
function wrap(task) {
    return (args) => {
        checkIsOutdated().catch((e) => builder_util_1.log.warn({ error: e }, "cannot check updates"));
        (0, load_1.loadEnv)(path.join(process.cwd(), "electron-builder.env"))
            .then(() => task(args))
            .catch(error => {
            process.exitCode = 1;
            // https://github.com/electron-userland/electron-builder/issues/2940
            process.on("exit", () => (process.exitCode = 1));
            if (error instanceof builder_util_1.InvalidConfigurationError) {
                builder_util_1.log.error(null, error.message);
            }
            else if (!(error instanceof builder_util_1.ExecError) || !error.alreadyLogged) {
                builder_util_1.log.error({ failedTask: task.name, stackTrace: error.stack }, error.message);
            }
        });
    };
}
async function checkIsOutdated() {
    if (isCi || process.env.NO_UPDATE_NOTIFIER != null) {
        return;
    }
    const pkg = await (0, fs_extra_1.readJson)(path.join(__dirname, "..", "..", "package.json"));
    if (pkg.version === "0.0.0-semantic-release") {
        return;
    }
    const UpdateNotifier = require("simple-update-notifier");
    await UpdateNotifier({ pkg });
}
async function rebuildAppNativeCode(args) {
    const projectDir = process.cwd();
    // this script must be used only for electron
    return (0, yarn_1.nodeGypRebuild)(args.platform, args.arch, { version: await (0, electronVersion_1.getElectronVersion)(projectDir), useCustomDist: true });
}
//# sourceMappingURL=cli.js.map