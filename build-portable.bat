@echo off

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo This script needs to run as administrator.
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ========================================
echo    Hookah Ads - Building Portable App
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found. Checking npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm is not available
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Clearing electron-builder cache...
npx electron-builder install-app-deps
if errorlevel 1 (
    echo Warning: Could not clear cache, continuing...
)

echo.
echo Building portable executable...
npm run build-portable
if errorlevel 1 (
    echo ERROR: Build failed
    echo.
    echo Trying alternative build method...
    npx electron-builder --win portable --config.compression=store --config.win.sign=false
    if errorlevel 1 (
        echo ERROR: Alternative build also failed
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo           BUILD SUCCESSFUL!
echo ========================================
echo.
echo Your portable app is ready in the 'dist' folder:
echo - HookahAds-Portable.exe
echo.
echo You can copy this file to any Windows computer
echo and run it without installation.
echo.
pause
