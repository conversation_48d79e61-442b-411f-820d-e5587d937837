{"version": 3, "file": "PlatformSpecificBuildOptions.js", "sourceRoot": "", "sources": ["../../src/options/PlatformSpecificBuildOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { CompressionLevel, Publish, TargetConfiguration, TargetSpecificOptions } from \"../core\"\nimport { FileAssociation } from \"./FileAssociation\"\n\nexport interface FileSet {\n  /**\n   * The source path relative to and defaults to:\n   *\n   *  - the [app directory](configuration.md#directories) for `files`,\n   *  - the project directory for `extraResources` and `extraFiles`.\n   * If you don't use two-package.json structure and don't set custom app directory, app directory equals to project directory.\n   */\n  from?: string\n  /**\n   * The destination path relative to and defaults to:\n   *\n   *  - the asar archive root for `files`,\n   *  - the app's content directory for `extraFiles`,\n   *  - the app's resource directory for `extraResources`.\n   */\n  to?: string\n  /**\n   * The [glob patterns](./file-patterns.md). Defaults to \"**\\/*\"\n   */\n  filter?: Array<string> | string\n}\n\nexport interface AsarOptions {\n  /**\n   * Whether to automatically unpack executables files.\n   * @default true\n   */\n  smartUnpack?: boolean\n\n  ordering?: string | null\n}\n\nexport interface FilesBuildOptions {\n  /**\n   * A [glob patterns](./file-patterns.md) relative to the [app directory](configuration.md#directories), which specifies which files to include when copying files to create the package.\n\nDefaults to:\n```json\n[\n  \"**\\/*\",\n  \"!**\\/node_modules/*\\/{CHANGELOG.md,README.md,README,readme.md,readme}\",\n  \"!**\\/node_modules/*\\/{test,__tests__,tests,powered-test,example,examples}\",\n  \"!**\\/node_modules/*.d.ts\",\n  \"!**\\/node_modules/.bin\",\n  \"!**\\/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}\",\n  \"!.editorconfig\",\n  \"!**\\/._*\",\n  \"!**\\/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}\",\n  \"!**\\/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}\",\n  \"!**\\/{appveyor.yml,.travis.yml,circle.yml}\",\n  \"!**\\/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}\"\n]\n```\n\nDevelopment dependencies are never copied in any case. You don't need to ignore it explicitly. Hidden files are not ignored by default, but all files that should be ignored, are ignored by default.\n\nDefault pattern \\`**\\/*\\` **is not added to your custom** if some of your patterns is not ignore (i.e. not starts with `!`). `package.json` and \\`**\\/node_modules/**\\/*` (only production dependencies will be copied) is added to your custom in any case. All default ignores are added in any case — you don't need to repeat it if you configure own patterns.\n\nMay be specified in the platform options (e.g. in the [mac](mac.md)).\n\nYou may also specify custom source and destination directories by using `FileSet` objects instead of simple glob patterns.\n\n```json\n[\n  {\n    \"from\": \"path/to/source\",\n    \"to\": \"path/to/destination\",\n    \"filter\": [\"**\\/*\", \"!foo/*.js\"]\n  }\n]\n```\n\nYou can use [file macros](./file-patterns.md#file-macros) in the `from` and `to` fields as well. `from` and `to` can be files and you can use this to [rename](https://github.com/electron-userland/electron-builder/issues/1119) a file while packaging.\n   */\n  files?: Array<FileSet | string> | FileSet | string | null\n\n  /**\n   * A [glob patterns](./file-patterns.md) relative to the project directory, when specified, copy the file or directory with matching names directly into the app's resources directory (`Contents/Resources` for MacOS, `resources` for Linux and Windows).\n   *\n   * File patterns (and support for `from` and `to` fields) the same as for [files](#files).\n   *\n   */\n  extraResources?: Array<FileSet | string> | FileSet | string | null\n\n  /**\n   * The same as [extraResources](#extraresources) but copy into the app's content directory (`Contents` for MacOS, root directory for Linux and Windows).\n   */\n  extraFiles?: Array<FileSet | string> | FileSet | string | null\n}\n\nexport interface PlatformSpecificBuildOptions extends TargetSpecificOptions, FilesBuildOptions {\n  /**\n   * The application id. Used as [CFBundleIdentifier](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/20001431-102070) for MacOS and as\n   * [Application User Model ID](https://msdn.microsoft.com/en-us/library/windows/desktop/dd378459(v=vs.85).aspx) for Windows (NSIS target only, Squirrel.Windows not supported). It is strongly recommended that an explicit ID is set.\n   * @default com.electron.${name}\n   */\n  readonly appId?: string | null\n\n  /**\n   * The [artifact file name template](./configuration.md#artifact-file-name-template). Defaults to `${productName}-${version}.${ext}` (some target can have other defaults, see corresponding options).\n   */\n  readonly artifactName?: string | null\n\n  /**\n   * The executable name. Defaults to `productName`.\n   */\n  readonly executableName?: string | null\n\n  /**\n   * The compression level. If you want to rapidly test build, `store` can reduce build time significantly. `maximum` doesn't lead to noticeable size difference, but increase build time.\n   * @default normal\n   */\n  readonly compression?: CompressionLevel | null\n\n  /**\n   * Whether to exclude all default ignored files(https://www.electron.build/contents#files) and options. Defaults to `false`.\n   *\n   * @default false\n   */\n  disableDefaultIgnoredFiles?: boolean | null\n\n  /**\n   * Whether to package the application's source code into an archive, using [Electron's archive format](http://electron.atom.io/docs/tutorial/application-packaging/).\n   *\n   * Node modules, that must be unpacked, will be detected automatically, you don't need to explicitly set [asarUnpack](#asarUnpack) - please file an issue if this doesn't work.\n   * @default true\n   */\n  readonly asar?: AsarOptions | boolean | null\n\n  /**\n   * A [glob patterns](./file-patterns.md) relative to the [app directory](#directories), which specifies which files to unpack when creating the [asar](http://electron.atom.io/docs/tutorial/application-packaging/) archive.\n   */\n  readonly asarUnpack?: Array<string> | string | null\n\n  /*  - @private */\n  readonly icon?: string | null\n\n  /**\n   * The file associations.\n   */\n  readonly fileAssociations?: Array<FileAssociation> | FileAssociation\n  /**\n   * The URL protocol schemes.\n   */\n  readonly protocols?: Array<Protocol> | Protocol\n\n  /**\n   * The electron locales to keep. By default, all Electron locales used as-is.\n   */\n  readonly electronLanguages?: Array<string> | string\n\n  /**\n   * Whether to fail if app will be not code signed.\n   */\n  readonly forceCodeSigning?: boolean\n\n  /**\n   * The [electron-updater compatibility](./auto-update.md#compatibility) semver range.\n   */\n  readonly electronUpdaterCompatibility?: string | null\n\n  /**\n   * Publisher configuration. See [Auto Update](./publish.md) for more information.\n   */\n  publish?: Publish\n\n  /**\n   * Whether to infer update channel from application version pre-release components. e.g. if version `0.12.1-alpha.1`, channel will be set to `alpha`. Otherwise to `latest`.\n   * This does *not* apply to github publishing, which will [never auto-detect the update channel](https://github.com/electron-userland/electron-builder/issues/8589).\n   * @default true\n   */\n  readonly detectUpdateChannel?: boolean\n\n  /**\n   * Please see [Building and Releasing using Channels](https://github.com/electron-userland/electron-builder/issues/1182#issuecomment-324947139).\n   * @default false\n   */\n  readonly generateUpdatesFilesForAllChannels?: boolean\n\n  /**\n   * The release info. Intended for command line usage:\n   *\n   * ```\n   * -c.releaseInfo.releaseNotes=\"new features\"\n   * ```\n   */\n  readonly releaseInfo?: ReleaseInfo\n\n  readonly target?: Array<string | TargetConfiguration> | string | TargetConfiguration | null\n\n  /*  - @private */\n  cscLink?: string | null\n\n  /*  - @private */\n  cscKeyPassword?: string | null\n\n  readonly defaultArch?: string\n}\n\nexport interface ReleaseInfo {\n  /**\n   * The release name.\n   */\n  releaseName?: string | null\n\n  /**\n   * The release notes.\n   */\n  releaseNotes?: string | null\n\n  /**\n   * The path to release notes file. Defaults to `release-notes-${platform}.md` (where `platform` it is current platform — `mac`, `linux` or `windows`) or `release-notes.md` in the [build resources](./contents.md#extraresources).\n   */\n  releaseNotesFile?: string | null\n\n  /**\n   * The release date.\n   */\n  releaseDate?: string\n\n  /**\n   * Vendor specific information.\n   */\n  vendor?: { [key: string]: any } | null\n}\n\n/**\n * URL Protocol Schemes. Protocols to associate the app with. macOS only.\n *\n * Please note — on macOS [you need to register an `open-url` event handler](http://electron.atom.io/docs/api/app/#event-open-url-macos).\n */\nexport interface Protocol {\n  /**\n   * The name. e.g. `IRC server URL`.\n   */\n  readonly name: string\n\n  /**\n   * The schemes. e.g. `[\"irc\", \"ircs\"]`.\n   */\n  readonly schemes: Array<string>\n\n  /**\n   * *macOS-only* The app’s role with respect to the type.\n   * @default Editor\n   */\n  readonly role?: \"Editor\" | \"Viewer\" | \"Shell\" | \"None\"\n}\n"]}