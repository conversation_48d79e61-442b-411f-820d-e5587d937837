"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLazyProductionDeps = createLazyProductionDeps;
const lazy_val_1 = require("lazy-val");
const appBuilder_1 = require("./appBuilder");
function createLazyProductionDeps(projectDir, excludedDependencies, flatten) {
    return new lazy_val_1.Lazy(async () => {
        const args = ["node-dep-tree", "--dir", projectDir];
        if (flatten)
            args.push("--flatten");
        if (excludedDependencies != null) {
            for (const name of excludedDependencies) {
                args.push("--exclude-dep", name);
            }
        }
        return (0, appBuilder_1.executeAppBuilderAsJson)(args);
    });
}
//# sourceMappingURL=packageDependencies.js.map