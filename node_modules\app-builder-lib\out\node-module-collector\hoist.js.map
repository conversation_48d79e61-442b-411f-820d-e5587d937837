{"version": 3, "file": "hoist.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/hoist.ts"], "names": [], "mappings": ";AAAA,8FAA8F;AAC9F;;;;;;;;;;;;;;;;;;;;;;;;;;EA0BE;;;AAoDF,IAAY,qBAIX;AAJD,WAAY,qBAAqB;IAC/B,uEAAO,CAAA;IACP,2EAAS,CAAA;IACT,6FAAkB,CAAA;AACpB,CAAC,EAJW,qBAAqB,qCAArB,qBAAqB,QAIhC;AAuCD,IAAK,SAIJ;AAJD,WAAK,SAAS;IACZ,uCAAG,CAAA;IACH,qCAAE,CAAA;IACF,+CAAO,CAAA;AACT,CAAC,EAJI,SAAS,KAAT,SAAS,QAIb;AAiBD,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,SAAiB,EAAE,EAAE,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,CAAA;AAC/E,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,SAAiB,EAAE,EAAE;IACpD,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;IACtC,uEAAuE;IACvE,MAAM,aAAa,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IACjF,OAAO,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;AACzC,CAAC,CAAA;AAED,IAAK,UAMJ;AAND,WAAK,UAAU;IACb,4CAAS,CAAA;IACT,2CAAQ,CAAA;IACR,6CAAS,CAAA;IACT,iDAAW,CAAA;IACX,iEAAmB,CAAA;AACrB,CAAC,EANI,UAAU,KAAV,UAAU,QAMd;AAkBD;;;;;;;;;GASG;AACI,MAAM,KAAK,GAAG,CAAC,IAAiB,EAAE,OAAqB,EAAE,EAAiB,EAAE;IACjF,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,UAAU,CAAC,IAAI,CAAC,CAAA;IAC3F,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,UAAU,IAAK,UAAU,CAAC,eAA0B,CAAA;IAChF,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,GAAG,EAAE,CAAA;IACvD,MAAM,OAAO,GAAyB,EAAE,KAAK,EAAE,UAAU,EAAE,cAAc,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAA;IACrG,IAAI,SAAiB,CAAA;IAErB,IAAI,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI;QAAE,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IAEjE,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAEzC,IAAI,kBAAkB,GAAG,KAAK,CAAA;IAC9B,IAAI,KAAK,GAAG,CAAC,CAAA;IACb,GAAG,CAAC;QACF,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE,OAAO,CAAC,CAAA;QAC7F,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,cAAc,CAAA;QACvE,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAA;QAClC,KAAK,EAAE,CAAA;IACT,CAAC,QAAQ,kBAAkB,EAAC;IAE5B,IAAI,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI;QAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,GAAG,EAAE,GAAG,SAAU,eAAe,KAAK,EAAE,CAAC,CAAA;IAEpH,IAAI,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;QAC3C,MAAM,YAAY,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAA;QAC1C,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,cAAc,CAAA;QACpH,IAAI,cAAc;YAAE,MAAM,IAAI,KAAK,CAAC,oDAAoD,YAAY,iBAAiB,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC7I,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAA;QACpC,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,+BAA+B,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QACpF,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,OAAO;QAAE,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAA;IAEhF,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAA;AAC7B,CAAC,CAAA;AAnCY,QAAA,KAAK,SAmCjB;AAED,MAAM,4BAA4B,GAAG,CAAC,YAAoC,EAAqC,EAAE;IAC/G,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACtD,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAA;IAClC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAmB,CAAA;IAE5C,MAAM,mBAAmB,GAAG,CAAC,IAAqB,EAAE,EAAE;QACpD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;YAAE,OAAM;QAC/B,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAEnB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;YAAE,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAExF,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,mBAAmB,CAAC,GAAG,CAAC,CAAA;YAC1B,CAAC;QACH,CAAC;IACH,CAAC,CAAA;IAED,mBAAmB,CAAC,QAAQ,CAAC,CAAA;IAE7B,OAAO,gBAAgB,CAAA;AACzB,CAAC,CAAA;AAED,MAAM,mBAAmB,GAAG,CAAC,YAAoC,EAAqC,EAAE;IACtG,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACtD,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAA;IAClC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAmB,CAAA;IAE5C,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAe,CAAA;IACjD,MAAM,mBAAmB,GAAG,CAAC,IAAqB,EAAE,kBAAoC,EAAE,EAAE;QAC1F,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;YAAE,OAAM;QAC/B,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAEnB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC;YACpD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtC,IAAI,mBAAmB,CAAA;gBACvB,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;oBAChC,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;oBACrD,IAAI,mBAAmB,EAAE,CAAC;wBACxB,gBAAgB,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAA;oBACrE,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,0BAA0B,GAAG,IAAI,GAAG,EAAe,CAAA;QAEzD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAAE,0BAA0B,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAEtF,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,mBAAmB,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAA;YACtD,CAAC;QACH,CAAC;IACH,CAAC,CAAA;IAED,mBAAmB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;IAEjD,OAAO,gBAAgB,CAAA;AACzB,CAAC,CAAA;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,iBAAiB,GAAG,CAAC,MAAuB,EAAE,IAAqB,EAAmB,EAAE;IAC5F,IAAI,IAAI,CAAC,SAAS;QAAE,OAAO,IAAI,CAAA;IAE/B,MAAM,EACJ,IAAI,EACJ,UAAU,EACV,KAAK,EACL,OAAO,EACP,YAAY,EACZ,oBAAoB,EACpB,mBAAmB,EACnB,SAAS,EACT,OAAO,EACP,aAAa,EACb,aAAa,EACb,cAAc,EACd,WAAW,EACX,SAAS,GACV,GAAG,IAAI,CAAA;IACR,4FAA4F;IAC5F,2FAA2F;IAC3F,sBAAsB;IACtB,MAAM,KAAK,GAAG;QACZ,IAAI;QACJ,UAAU,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC;QAC/B,KAAK;QACL,OAAO;QACP,YAAY,EAAE,IAAI,GAAG,CAAC,YAAY,CAAC;QACnC,oBAAoB,EAAE,IAAI,GAAG,CAAC,oBAAoB,CAAC;QACnD,mBAAmB,EAAE,IAAI,GAAG,CAAC,mBAAmB,CAAC;QACjD,SAAS,EAAE,IAAI,GAAG,CAAC,SAAS,CAAC;QAC7B,OAAO,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC;QACzB,SAAS,EAAE,IAAI;QACf,aAAa;QACb,aAAa;QACb,cAAc;QACd,WAAW,EAAE,IAAI,GAAG,CAAC,WAAW,CAAC;QACjC,SAAS,EAAE,IAAI,GAAG,CAAC,SAAS,CAAC;KAC9B,CAAA;IACD,MAAM,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC5C,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK;QACzC,wBAAwB;QACxB,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IAErC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IAE1C,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAED;;;;;;;;;GASG;AACH,MAAM,gBAAgB,GAAG,CAAC,QAAyB,EAAE,aAA4B,EAAkC,EAAE;IACnH,MAAM,QAAQ,GAAG,IAAI,GAAG,CAA4B,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAExF,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;QACjD,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAA;QACrC,CAAC;IACH,CAAC;IAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAA;IAChD,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;QAC1B,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAE,CAAA;QACvC,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAE,CAAA;QACvC,IAAI,MAAM,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,EAAE,CAAC;YAClD,OAAO,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAA;QACpD,CAAC;aAAM,CAAC;YACN,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAA;YACxE,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAA;YACxE,OAAO,YAAY,GAAG,YAAY,CAAA;QACpC,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;QAClD,MAAM,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAC5C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,IAAI,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,GAAG,EAAE,CAAA;gBACX,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;YAC5B,CAAC;YACD,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAA;AACjB,CAAC,CAAA;AAED;;;;;;GAMG;AACH,MAAM,4BAA4B,GAAG,CAAC,IAAqB,EAAwB,EAAE;IACnF,MAAM,YAAY,GAAyB,IAAI,GAAG,EAAE,CAAA;IAEpD,MAAM,MAAM,GAAG,CAAC,GAAoB,EAAE,QAAQ,GAAG,IAAI,GAAG,EAAE,EAAE,EAAE;QAC5D,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC;YAAE,OAAM;QAC7B,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAEjB,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;gBAC/C,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC1C,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;gBAC3B,CAAC;YACH,CAAC;QACH,CAAC;QACD,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACvB,CAAC,CAAA;IAED,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,MAAM,CAAC,GAAG,CAAC,CAAA;QACb,CAAC;IACH,CAAC;IAED,OAAO,YAAY,CAAA;AACrB,CAAC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,OAAO,GAAG,CACd,IAAqB,EACrB,YAAoC,EACpC,oBAAkC,EAClC,mBAAkC,EAClC,OAA6B,EAC7B,YAAkC,IAAI,GAAG,EAAE,EACe,EAAE;IAC5D,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACtD,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;QAAE,OAAO,EAAE,kBAAkB,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,CAAA;IACxF,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAEvB,MAAM,aAAa,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAA;IAElD,MAAM,aAAa,GAAG,gBAAgB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;IAE/D,MAAM,gBAAgB,GAAG,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAA;IAEnK,IAAI,eAAe,CAAA;IAEnB,IAAI,kBAAkB,GAAG,KAAK,CAAA;IAC9B,IAAI,cAAc,GAAG,KAAK,CAAA;IAE1B,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3F,MAAM,aAAa,GAAkB,IAAI,GAAG,EAAE,CAAA;IAC9C,GAAG,CAAC;QACF,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,EAAE,YAAY,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,mBAAmB,EAAE,aAAa,EAAE,OAAO,CAAC,CAAA;QAC9J,IAAI,MAAM,CAAC,cAAc;YAAE,cAAc,GAAG,IAAI,CAAA;QAChD,IAAI,MAAM,CAAC,kBAAkB;YAAE,kBAAkB,GAAG,IAAI,CAAA;QAExD,eAAe,GAAG,KAAK,CAAA;QACvB,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,aAAa,EAAE,CAAC;YAC3C,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1D,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;gBACxB,MAAM,CAAC,KAAK,EAAE,CAAA;gBACd,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;gBAChC,eAAe,GAAG,IAAI,CAAA;YACxB,CAAC;QACH,CAAC;IACH,CAAC,QAAQ,eAAe,EAAC;IAEzB,KAAK,MAAM,UAAU,IAAI,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;QACxD,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9F,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;YAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,YAAY,EAAE,UAAU,CAAC,EAAE,oBAAoB,EAAE,aAAa,EAAE,OAAO,CAAC,CAAA;YACzG,IAAI,MAAM,CAAC,cAAc;gBAAE,cAAc,GAAG,IAAI,CAAA;YAChD,IAAI,MAAM,CAAC,kBAAkB;gBAAE,kBAAkB,GAAG,IAAI,CAAA;YAExD,oBAAoB,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAED,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,CAAA;AAC/C,CAAC,CAAA;AAED,MAAM,wBAAwB,GAAG,CAAC,IAAqB,EAAW,EAAE;IAClE,KAAK,MAAM,CAAC,OAAO,EAAE,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QACzD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;YACvE,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAED,MAAM,gBAAgB,GAAG,CACvB,QAAyB,EACzB,oBAAkC,EAClC,QAAgC,EAChC,IAAqB,EACrB,gBAAmD,EACnD,WAAoC,EACpC,aAAuC,EACvC,aAA4B,EAC5B,EAAE,YAAY,EAAE,kBAAkB,EAA0D,EACjF,EAAE;IACb,IAAI,UAAU,CAAA;IACd,IAAI,MAAM,GAAkB,IAAI,CAAA;IAChC,IAAI,SAAS,GAAgC,IAAI,GAAG,EAAE,CAAA;IACtD,IAAI,YAAY;QACd,UAAU,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC;aAC7C,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;aAC/B,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;IAEhB,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAChD,kCAAkC;IAClC,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,CAAA;IACvD,IAAI,WAAW,GAAG,CAAC,eAAe,CAAA;IAClC,IAAI,YAAY,IAAI,CAAC,WAAW;QAAE,MAAM,GAAG,kBAAkB,CAAA;IAE7D,IAAI,WAAW,EAAE,CAAC;QAChB,WAAW,GAAG,IAAI,CAAC,cAAc,KAAK,qBAAqB,CAAC,SAAS,CAAA;QACrE,IAAI,YAAY,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,GAAG,aAAa,CAAA;QACxB,CAAC;IACH,CAAC;IAED,IAAI,WAAW,IAAI,IAAI,CAAC,cAAc,KAAK,qBAAqB,CAAC,kBAAkB,EAAE,CAAC;QACpF,WAAW,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAA;QAC7C,IAAI,YAAY,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,GAAG,kDAAkD,CAAA;QAC7D,CAAC;IACH,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QAChB,WAAW,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,YAAY,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,GAAG,yBAAyB,kBAAkB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAE,CAAC,OAAO,CAAC,OAAO,UAAU,EAAE,CAAA;QAChI,CAAC;IACH,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QAChB,IAAI,eAAe,GAAG,KAAK,CAAA;QAC3B,MAAM,OAAO,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/C,eAAe,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAA;QAC1D,IAAI,YAAY,IAAI,CAAC,eAAe;YAAE,MAAM,GAAG,gBAAgB,kBAAkB,CAAC,OAAQ,CAAC,OAAO,CAAC,OAAO,UAAU,EAAE,CAAA;QACtH,IAAI,eAAe,EAAE,CAAC;YACpB,KAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC;gBACpD,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAA;gBAC5B,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACpD,IAAI,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;oBAChD,eAAe,GAAG,KAAK,CAAA;oBACvB,IAAI,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;oBACjD,IAAI,CAAC,aAAa,EAAE,CAAC;wBACnB,aAAa,GAAG,IAAI,GAAG,EAAE,CAAA;wBACzB,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAA;oBAC9C,CAAC;oBACD,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAC5B,IAAI,YAAY;wBACd,MAAM,GAAG,eAAe,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,QAAQ;6BACzE,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;6BACb,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;6BACvC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;oBAChB,MAAK;gBACP,CAAC;YACH,CAAC;QACH,CAAC;QAED,WAAW,GAAG,eAAe,CAAA;IAC/B,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QAChB,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/C,WAAW,GAAG,YAAY,KAAK,IAAI,CAAC,KAAK,CAAA;QACzC,IAAI,YAAY,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,GAAG,gBAAgB,kBAAkB,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,OAAO,UAAU,EAAE,CAAA;QAClG,CAAC;IACH,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QAChB,IAAI,oBAAoB,GAAG,IAAI,CAAA;QAC/B,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACzC,KAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC;YACpD,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC5B,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;gBAC7B,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC;oBAAE,SAAQ;gBAEjF,MAAM,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBACnD,IAAI,aAAa,IAAI,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,aAAa,EAAE,CAAC;oBACvE,IAAI,GAAG,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAChC,SAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;oBAC/B,CAAC;yBAAM,CAAC;wBACN,SAAS,GAAG,IAAI,CAAA;wBAChB,oBAAoB,GAAG,KAAK,CAAA;wBAC5B,IAAI,YAAY,EAAE,CAAC;4BACjB,MAAM,GAAG,qBAAqB,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,UAAU,EAAE,CAAA;wBAC9J,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YACxB,CAAC;YACD,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC1B,MAAK;YACP,CAAC;QACH,CAAC;QACD,WAAW,GAAG,oBAAoB,CAAA;IACpC,CAAC;IAED,IAAI,WAAW,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACvC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC;YACxD,MAAM,OAAO,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC7F,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE,CAAC;gBAChD,WAAW,GAAG,KAAK,CAAA;gBACnB,IAAI,YAAY;oBAAE,MAAM,GAAG,qDAAqD,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,kBAAkB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,EAAE,CAAA;gBAEzK,MAAK;YACP,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;QAC7C,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,CAAA;IAC9D,CAAC;SAAM,CAAC;QACN,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,CAAA;IAC5E,CAAC;AACH,CAAC,CAAA;AAED,MAAM,iBAAiB,GAAG,CAAC,IAAqB,EAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAoB,CAAA;AAErH;;;;;;;;GAQG;AACH,MAAM,UAAU,GAAG,CACjB,IAAqB,EACrB,YAAoC,EACpC,oBAAkC,EAClC,gBAAmD,EACnD,WAAoC,EACpC,aAAuC,EACvC,mBAAkC,EAClC,aAA4B,EAC5B,OAA6B,EAC6B,EAAE;IAC5D,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACtD,MAAM,SAAS,GAAG,IAAI,GAAG,EAAmB,CAAA;IAC5C,IAAI,kBAAkB,GAAG,KAAK,CAAA;IAC9B,IAAI,cAAc,GAAG,KAAK,CAAA;IAE1B,MAAM,qBAAqB,GAAG,CAC5B,QAAgC,EAChC,WAA2B,EAC3B,kBAAyC,EACzC,UAA2B,EAC3B,QAA8B,EAC9B,EAAE;QACF,IAAI,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC;YAAE,OAAM;QACrC,MAAM,eAAe,GAAG,CAAC,GAAG,WAAW,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAA;QACvE,MAAM,sBAAsB,GAAG,CAAC,GAAG,kBAAkB,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAA;QAErF,MAAM,aAAa,GAAG,IAAI,GAAG,EAAiC,CAAA;QAC9D,MAAM,UAAU,GAAG,IAAI,GAAG,EAA8B,CAAA;QACxD,KAAK,MAAM,aAAa,IAAI,4BAA4B,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,MAAM,SAAS,GAAG,gBAAgB,CAChC,QAAQ,EACR,oBAAoB,EACpB,CAAC,QAAQ,EAAE,GAAG,QAAQ,EAAE,UAAU,CAAC,EACnC,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,aAAa,EACb,aAAa,EACb,EAAE,YAAY,EAAE,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,OAAO,EAAE,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,EAAE,CAC3G,CAAA;YAED,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;YACxC,IAAI,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;gBAChD,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;oBACvC,MAAM,cAAc,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE,CAAA;oBAChE,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;oBACtC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;gBAC9C,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAmB,CAAA;QACnD,MAAM,kBAAkB,GAAG,CAAC,IAAqB,EAAE,SAAoB,EAAE,MAAc,EAAE,EAAE;YACzF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBAC1B,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;gBAC3D,KAAK,MAAM,aAAa,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;oBAC/D,kBAAkB,CAChB,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAE,EAC3C,SAAS,EACT,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,OAAO;wBACtC,CAAC,CAAC,qBAAqB,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,kBAAkB,CAAC,UAAU,CAAC,OAAO,CAAC,kBAAkB;wBAC/H,CAAC,CAAC,EAAE,CACP,CAAA;gBACH,CAAC;YACH,CAAC;QACH,CAAC,CAAA;QAED,KAAK,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,UAAU;YAAE,IAAI,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,EAAE;gBAAE,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,MAAO,CAAC,CAAA;QAE9I,IAAI,gBAAgB,GAAG,KAAK,CAAA;QAC5B,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChC,cAAc,GAAG,IAAI,CAAA;gBACrB,MAAM,aAAa,GAAG,mBAAmB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;gBACzD,IAAI,aAAa,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;oBAAE,kBAAkB,GAAG,IAAI,CAAA;gBAE5E,gBAAgB,GAAG,IAAI,CAAA;gBACvB,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACzC,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBACnD,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEpC,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACxD,IAAI,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;oBAC7C,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;yBACxC,MAAM,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;yBAC5B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;yBAC/B,IAAI,CAAC,GAAG,CAAC,CAAA;oBACZ,IAAI,gBAAgB,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAC1D,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACtB,gBAAgB,GAAG,EAAE,CAAA;wBACrB,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAA;oBACvD,CAAC;oBACD,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;oBAElC,UAAU,CAAC,SAAS,CAAC,GAAG,CACtB,IAAI,CAAC,IAAI,EACT,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC;yBACrB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;yBACvC,IAAI,CAAC,GAAG,CAAC,CACb,CAAA;gBACH,CAAC;gBACD,iEAAiE;gBACjE,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,oDAAoD;oBACpD,IAAI,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;wBAClC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;wBAC1C,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;oBACpB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBACxC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;oBACvC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,cAAc,KAAK,qBAAqB,CAAC,kBAAkB,IAAI,gBAAgB;YAAE,kBAAkB,GAAG,IAAI,CAAA;QAEzH,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;YAChC,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CACb,GAAG,QAAQ,oCAAoC,CAAC,QAAQ,EAAE,GAAG,QAAQ,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,EAAE,CAC9J,CAAA;YACH,CAAC;QACH,CAAC;QAED,MAAM,QAAQ,GAAG,4BAA4B,CAAC,UAAU,CAAC,CAAA;QACzD,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/B,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAE,CAAA;gBACvC,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACjD,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,GAAG;oBAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,MAAO,CAAC,CAAA;gBAE1K,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,sBAAsB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvF,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;oBACzB,MAAM,aAAa,GAAG,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;oBAEzD,qBAAqB,CAAC,CAAC,GAAG,QAAQ,EAAE,UAAU,CAAC,EAAE,eAAe,EAAE,sBAAsB,EAAE,aAAa,EAAE,YAAY,CAAC,CAAA;oBAEtH,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,CAAA;IAED,IAAI,QAAQ,CAAA;IACZ,IAAI,YAAY,GAAG,IAAI,GAAG,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC,CAAA;IAClE,MAAM,2BAA2B,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3F,GAAG,CAAC;QACF,QAAQ,GAAG,YAAY,CAAA;QACvB,YAAY,GAAG,IAAI,GAAG,EAAE,CAAA;QACxB,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC3B,IAAI,GAAG,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,IAAI,GAAG,CAAC,aAAa;gBAAE,SAAQ;YACnE,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;YAE5D,qBAAqB,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,2BAA2B,EAAE,mBAAmB,EAAE,YAAY,CAAC,CAAA;QAC7H,CAAC;IACH,CAAC,QAAQ,YAAY,CAAC,IAAI,GAAG,CAAC,EAAC;IAE/B,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,CAAA;AAC/C,CAAC,CAAA;AAED,MAAM,SAAS,GAAG,CAAC,IAAqB,EAAU,EAAE;IAClD,MAAM,GAAG,GAAkB,EAAE,CAAA;IAE7B,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAA;IAC3B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAmB,CAAA;IAE1C,MAAM,SAAS,GAAG,CAAC,IAAqB,EAAE,UAA6C,EAAE,MAAuB,EAAE,EAAE;QAClH,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;YAAE,OAAM;QAC/B,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAEnB,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;YAAE,OAAM;QAE7B,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAA;QACxC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;gBAAE,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAEhH,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,EAAE,CAAC;YACzD,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC1C,MAAM,mBAAmB,GAAG,GAAG,EAAE,CAC/B,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;iBACnB,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;iBACd,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;iBACvC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;YAChB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;gBAC9C,IAAI,SAAS,KAAK,GAAG,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE,CAAC;oBACzE,GAAG,CAAC,IAAI,CAAC,GAAG,mBAAmB,EAAE,oCAAoC,OAAO,CAAC,KAAK,cAAc,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAA;gBAC5I,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACrD,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;gBAC1D,MAAM,iBAAiB,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,iBAAiB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;gBAC3F,MAAM,uBAAuB,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,eAAe,iBAAiB,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;gBAChG,MAAM,cAAc,GAAG,GAAG,mBAAmB,EAAE,GAAG,iBAAiB,EAAE,CAAA;gBACrE,IAAI,CAAC,GAAG,EAAE,CAAC;oBACT,GAAG,CAAC,IAAI,CAAC,GAAG,cAAc,qDAAqD,OAAO,CAAC,IAAI,GAAG,uBAAuB,QAAQ,CAAC,CAAA;gBAChI,CAAC;qBAAM,IAAI,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE,CAAC;oBACvC,GAAG,CAAC,IAAI,CAAC,GAAG,cAAc,iCAAiC,OAAO,CAAC,IAAI,GAAG,uBAAuB,cAAc,OAAO,CAAC,KAAK,gBAAgB,GAAG,CAAC,KAAK,EAAE,CAAC,CAAA;gBAC1J,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACjB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,SAAS,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,CAAA;YACpC,CAAC;QACH,CAAC;QACD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACtB,CAAC,CAAA;IAED,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;IAExC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACvB,CAAC,CAAA;AAED;;;;GAIG;AACH,MAAM,SAAS,GAAG,CAAC,IAAiB,EAAE,OAA6B,EAAmB,EAAE;IACtF,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,CAAA;IACtD,MAAM,QAAQ,GAAoB;QAChC,IAAI;QACJ,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;QAChC,OAAO,EAAE,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC;QAC1C,KAAK,EAAE,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC;QACtC,YAAY,EAAE,IAAI,GAAG,EAAE;QACvB,oBAAoB,EAAE,IAAI,GAAG,EAAE;QAC/B,mBAAmB,EAAE,IAAI,GAAG,EAAE;QAC9B,SAAS,EAAE,IAAI,GAAG,CAAC,SAAS,CAAC;QAC7B,OAAO,EAAE,IAAI,GAAG,EAAE;QAClB,SAAS,EAAE,IAAI;QACf,aAAa,EAAE,IAAI;QACnB,aAAa,EAAE,CAAC;QAChB,cAAc,EAAE,qBAAqB,CAAC,SAAS;QAC/C,WAAW,EAAE,IAAI,GAAG,EAAE;QACtB,SAAS,EAAE,IAAI,GAAG,EAAE;KACrB,CAAA;IAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAA+B,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAA;IAE3E,MAAM,OAAO,GAAG,CAAC,IAAiB,EAAE,UAA2B,EAAE,EAAE;QACjE,IAAI,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAClC,MAAM,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAA;QACzB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,IAAI,CAAA;YACrF,MAAM,4BAA4B,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;YACnF,QAAQ,GAAG;gBACT,IAAI;gBACJ,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;gBAChC,OAAO,EAAE,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC;gBAC1C,KAAK,EAAE,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC;gBACtC,YAAY,EAAE,IAAI,GAAG,EAAE;gBACvB,oBAAoB,EAAE,IAAI,GAAG,EAAE;gBAC/B,mBAAmB,EAAE,IAAI,GAAG,EAAE;gBAC9B,SAAS,EAAE,IAAI,GAAG,CAAC,SAAS,CAAC;gBAC7B,OAAO,EAAE,IAAI,GAAG,EAAE;gBAClB,SAAS,EAAE,IAAI;gBACf,aAAa,EAAE,4BAA4B,CAAC,CAAC,CAAC,4BAA4B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK;gBAC5F,aAAa,EAAE,aAAa,IAAI,CAAC;gBACjC,cAAc,EAAE,cAAc,IAAI,qBAAqB,CAAC,OAAO;gBAC/D,WAAW,EAAE,IAAI,GAAG,EAAE;gBACtB,SAAS,EAAE,IAAI,GAAG,EAAE;aACrB,CAAA;YACD,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;QAC/B,CAAC;QAED,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;QAChD,UAAU,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;QAExD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;YACxB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAA;YAElC,MAAM,eAAe,GAAG,CAAC,IAAqB,EAAE,EAAE;gBAChD,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC;oBAAE,OAAM;gBACtC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBAC1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;gBAEtB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;wBAClC,eAAe,CAAC,GAAG,CAAC,CAAA;oBACtB,CAAC;gBACH,CAAC;YACH,CAAC,CAAA;YAED,eAAe,CAAC,QAAQ,CAAC,CAAA;QAC3B,CAAC;IACH,CAAC,CAAA;IAED,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY;QAAE,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;IAE3D,OAAO,QAAQ,CAAA;AACjB,CAAC,CAAA;AAED,MAAM,YAAY,GAAG,CAAC,OAAgB,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;AAExF;;;;GAIG;AACH,MAAM,UAAU,GAAG,CAAC,IAAqB,EAAiB,EAAE;IAC1D,MAAM,QAAQ,GAAkB;QAC9B,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;QACrC,UAAU,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;QACpC,YAAY,EAAE,IAAI,GAAG,EAAE;KACxB,CAAA;IAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAkB,CAAC,IAAI,CAAC,CAAC,CAAA;IAElD,MAAM,OAAO,GAAG,CAAC,IAAqB,EAAE,cAA+B,EAAE,UAAyB,EAAE,EAAE;QACpG,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAElC,IAAI,UAAyB,CAAA;QAC7B,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;YAC5B,UAAU,GAAG,UAAU,CAAA;QACzB,CAAC;aAAM,CAAC;YACN,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,CAAA;YAC1C,UAAU,GAAG;gBACX,IAAI;gBACJ,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;gBAChC,UAAU;gBACV,YAAY,EAAE,IAAI,GAAG,EAAiB;aACvC,CAAA;QACH,CAAC;QACD,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;QAEvC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YACnB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;gBAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBAClC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAA;gBAChC,CAAC;YACH,CAAC;YACD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACxB,CAAC;IACH,CAAC,CAAA;IAED,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;QAAE,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;IAE1E,OAAO,QAAQ,CAAA;AACjB,CAAC,CAAA;AAED;;;;;;;GAOG;AACH,MAAM,kBAAkB,GAAG,CAAC,QAAyB,EAAiB,EAAE;IACtE,MAAM,aAAa,GAAkB,IAAI,GAAG,EAAE,CAAA;IAE9C,MAAM,SAAS,GAAG,IAAI,GAAG,CAAkB,CAAC,QAAQ,CAAC,CAAC,CAAA;IACtD,MAAM,gBAAgB,GAAG,CAAC,IAAqB,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAA;IAEhF,MAAM,0BAA0B,GAAG,CAAC,IAAqB,EAAE,EAAE;QAC3D,MAAM,GAAG,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAA;QAClC,IAAI,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,KAAK,GAAG,EAAE,UAAU,EAAE,IAAI,GAAG,EAAS,EAAE,cAAc,EAAE,IAAI,GAAG,EAAS,EAAE,aAAa,EAAE,CAAC,EAAE,CAAA;YAC5F,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QAC/B,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC,CAAA;IAED,MAAM,YAAY,GAAG,CAAC,SAA0B,EAAE,IAAqB,EAAE,EAAE;QACzE,MAAM,MAAM,GAAG,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAEpC,MAAM,KAAK,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAA;QAC9C,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QAErC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YACnB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;gBAC7C,MAAM,KAAK,GAAG,0BAA0B,CAAC,GAAG,CAAC,CAAA;gBAC7C,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,aAAa,CAAC,CAAA;gBACtE,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBACtC,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,CAAA;IAED,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;YAAE,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;IAEpH,OAAO,aAAa,CAAA;AACtB,CAAC,CAAA;AAED,MAAM,kBAAkB,GAAG,CAAC,OAAiB,EAAE,EAAE;IAC/C,IAAI,CAAC,OAAO;QAAE,OAAO,MAAM,CAAA;IAE3B,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;IACnC,IAAI,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;QAAE,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,CAAA;IAC1E,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;IAC5C,IAAI,SAAS,KAAK,aAAa,EAAE,CAAC;QAChC,OAAO,GAAG,CAAA;IACZ,CAAC;SAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACtB,OAAO,GAAG,IAAI,EAAE,CAAA;IAClB,CAAC;SAAM,CAAC;QACN,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;QACpG,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC;YAAE,IAAI,GAAG,KAAK,IAAI,EAAE,CAAA;QACvD,IAAI,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,IAAI,GAAG,KAAK,IAAI,EAAE,CAAA;YAClB,OAAO,GAAG,EAAE,CAAA;QACd,CAAC;QAED,OAAO,GAAG,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;IACjD,CAAC;AACH,CAAC,CAAA;AAED,MAAM,iBAAiB,GAAG,KAAK,CAAA;AAE/B;;;;;;;;GAQG;AAEH,MAAM,WAAW,GAAG,CAAC,IAAqB,EAAE,EAAE;IAC5C,IAAI,SAAS,GAAG,CAAC,CAAA;IACjB,MAAM,WAAW,GAAG,CAAC,GAAoB,EAAE,OAA6B,EAAE,MAAM,GAAG,EAAE,EAAU,EAAE;QAC/F,IAAI,SAAS,GAAG,iBAAiB,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;YAAE,OAAO,EAAE,CAAA;QAEhE,SAAS,EAAE,CAAA;QACX,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;YACzE,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;gBACxB,OAAO,CAAC,CAAA;YACV,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACnC,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,GAAG,GAAG,EAAE,CAAA;QACZ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAChB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC;YACnD,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;YAC7B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;gBAChD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBACxC,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;gBAC3C,GAAG,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAA;gBACtN,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,MAAM,GAAG,GAAG,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;YAC7F,CAAC;QACH,CAAC;QACD,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QACnB,OAAO,GAAG,CAAA;IACZ,CAAC,CAAA;IAED,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;IAE7C,OAAO,QAAQ,GAAG,CAAC,SAAS,GAAG,iBAAiB,CAAC,CAAC,CAAC,yDAAyD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;AACpH,CAAC,CAAA", "sourcesContent": ["// copy from https://github.com/yarnpkg/berry/blob/master/packages/yarnpkg-nm/sources/hoist.ts\n/**\nBSD 2-Clause License\n\nCopyright (c) 2016-present, Yarn Contributors.\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n1. Redistributions of source code must retain the above copyright notice, this\n   list of conditions and the following disclaimer.\n\n2. Redistributions in binary form must reproduce the above copyright notice,\n   this list of conditions and the following disclaimer in the documentation\n   and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\nFOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL\nDAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\nSERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\nCAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\nOR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n\n// commit: d63d411bcc5adcbffd198b8987c5a14c81eaf669\n// fix(nm): optimize hoisting by treating peer deps same as other deps (#6517)\n/**\n * High-level node_modules hoisting algorithm recipe\n *\n * 1. Take input dependency graph and start traversing it,\n * as you visit new node in the graph - clone it if there can be multiple paths\n * to access the node from the graph root to the node, e.g. essentially represent\n * the graph with a tree as you go, to make hoisting possible.\n * 2. You want to hoist every node possible to the top root node first,\n * then to each of its children etc, so you need to keep track what is your current\n * root node into which you are hoisting\n * 3. Traverse the dependency graph from the current root node and for each package name\n * that can be potentially hoisted to the current root node build a list of idents\n * in descending hoisting preference. You will check in next steps whether most preferred ident\n * for the given package name can be hoisted first, and if not, then you check the\n * less preferred ident, etc, until either some ident will be hoisted\n * or you run out of idents to check\n * (no need to convert the graph to the tree when you build this preference map).\n * 4. The children of the root node are already \"hoisted\", so you need to start\n * from the dependencies of these children. You take some child and\n * sort its dependencies so that regular dependencies without peer dependencies\n * will come first and then those dependencies that peer depend on them.\n * This is needed to make algorithm more efficient and hoist nodes which are easier\n * to hoist first and then handle peer dependent nodes.\n * 5. You take this sorted list of dependencies and check if each of them can be\n * hoisted to the current root node. To answer is the node can be hoisted you check\n * your constraints - require promise and peer dependency promise.\n * The possible answers can be: YES - the node is hoistable to the current root,\n * NO - the node is not hoistable to the current root\n * and DEPENDS - the node is hoistable to the root if nodes X, Y, Z are hoistable\n * to the root. The case DEPENDS happens when all the require and other\n * constraints are met, except peer dependency constraints. Note, that the nodes\n * that are not package idents currently at the top of preference list are considered\n * to have the answer NO right away, before doing any other constraint checks.\n * 6. When you have hoistable answer for each dependency of a node you then build\n * a list of nodes that are NOT hoistable. These are the nodes that have answer NO\n * and the nodes that DEPENDS on these nodes. All the other nodes are hoistable,\n * those that have answer YES and those that have answer DEPENDS,\n * because they are cyclically dependent on each another\n * 7. You hoist all the hoistable nodes to the current root and continue traversing\n * the tree. Note, you need to track newly added nodes to the current root,\n * because after you finished tree traversal you want to come back to these new nodes\n * first thing and hoist everything from each of them to the current tree root.\n * 8. After you have finished traversing newly hoisted current root nodes\n * it means you cannot hoist anything to the current tree root and you need to pick\n * the next node as current tree root and run the algorithm again\n * until you run out of candidates for current tree root.\n */\ntype PackageName = string\nexport enum HoisterDependencyKind {\n  REGULAR,\n  WORKSPACE,\n  EXTERNAL_SOFT_LINK,\n}\nexport type HoisterTree = {\n  name: PackageName\n  identName: PackageName\n  reference: string\n  dependencies: Set<HoisterTree>\n  peerNames: Set<PackageName>\n  hoistPriority?: number\n  dependencyKind?: HoisterDependencyKind\n}\nexport type HoisterResult = { name: PackageName; identName: PackageName; references: Set<string>; dependencies: Set<HoisterResult> }\ntype Locator = string\ntype AliasedLocator = string & { __aliasedLocator: true }\ntype Ident = string\ntype HoisterWorkTree = {\n  name: PackageName\n  references: Set<string>\n  ident: Ident\n  locator: Locator\n  dependencies: Map<PackageName, HoisterWorkTree>\n  originalDependencies: Map<PackageName, HoisterWorkTree>\n  hoistedDependencies: Map<PackageName, HoisterWorkTree>\n  peerNames: ReadonlySet<PackageName>\n  decoupled: boolean\n  reasons: Map<PackageName, string>\n  isHoistBorder: boolean\n  hoistedFrom: Map<PackageName, Array<string>>\n  hoistedTo: Map<PackageName, string>\n  hoistPriority: number\n  dependencyKind: HoisterDependencyKind\n}\n\n/**\n * Mapping which packages depend on a given package alias + ident. It is used to determine hoisting weight,\n * e.g. which one among the group of packages with the same name should be hoisted.\n * The package having the biggest number of parents using this package will be hoisted.\n */\ntype PreferenceMap = Map<string, { peerDependents: Set<Ident>; dependents: Set<Ident>; hoistPriority: number }>\n\nenum Hoistable {\n  YES,\n  NO,\n  DEPENDS,\n}\ntype HoistInfo =\n  | {\n      isHoistable: Hoistable.YES\n    }\n  | {\n      isHoistable: Hoistable.NO\n      reason: string | null\n    }\n  | {\n      isHoistable: Hoistable.DEPENDS\n      dependsOn: Set<HoisterWorkTree>\n      reason: string | null\n    }\n\ntype ShadowedNodes = Map<HoisterWorkTree, Set<PackageName>>\n\nconst makeLocator = (name: string, reference: string) => `${name}@${reference}`\nconst makeIdent = (name: string, reference: string) => {\n  const hashIdx = reference.indexOf(`#`)\n  // Strip virtual reference part, we don't need it for hoisting purposes\n  const realReference = hashIdx >= 0 ? reference.substring(hashIdx + 1) : reference\n  return makeLocator(name, realReference)\n}\n\nenum DebugLevel {\n  NONE = -1,\n  PERF = 0,\n  CHECK = 1,\n  REASONS = 2,\n  INTENSIVE_CHECK = 9,\n}\n\nexport type HoistOptions = {\n  /** Runs self-checks after hoisting is finished */\n  check?: boolean\n  /** Debug level */\n  debugLevel?: DebugLevel\n  /** Hoist borders are defined by parent node locator and its dependency name. The dependency is considered a border, nothing can be hoisted past this dependency, but dependency can be hoisted */\n  hoistingLimits?: Map<Locator, Set<PackageName>>\n}\n\ntype InternalHoistOptions = {\n  check?: boolean\n  debugLevel: DebugLevel\n  fastLookupPossible: boolean\n  hoistingLimits: Map<Locator, Set<PackageName>>\n}\n\n/**\n * Hoists package tree.\n *\n * The root node of a tree must has id: '.'.\n * This function does not mutate its arguments, it hoists and returns tree copy.\n *\n * @param tree package tree (cycles in the tree are allowed)\n *\n * @returns hoisted tree copy\n */\nexport const hoist = (tree: HoisterTree, opts: HoistOptions = {}): HoisterResult => {\n  const debugLevel = opts.debugLevel || Number(process.env.NM_DEBUG_LEVEL || DebugLevel.NONE)\n  const check = opts.check || debugLevel >= (DebugLevel.INTENSIVE_CHECK as number)\n  const hoistingLimits = opts.hoistingLimits || new Map()\n  const options: InternalHoistOptions = { check, debugLevel, hoistingLimits, fastLookupPossible: true }\n  let startTime: number\n\n  if (options.debugLevel >= DebugLevel.PERF) startTime = Date.now()\n\n  const treeCopy = cloneTree(tree, options)\n\n  let anotherRoundNeeded = false\n  let round = 0\n  do {\n    const result = hoistTo(treeCopy, [treeCopy], new Set([treeCopy.locator]), new Map(), options)\n    anotherRoundNeeded = result.anotherRoundNeeded || result.isGraphChanged\n    options.fastLookupPossible = false\n    round++\n  } while (anotherRoundNeeded)\n\n  if (options.debugLevel >= DebugLevel.PERF) console.log(`hoist time: ${Date.now() - startTime!}ms, rounds: ${round}`)\n\n  if (options.debugLevel >= DebugLevel.CHECK) {\n    const prevTreeDump = dumpDepTree(treeCopy)\n    const isGraphChanged = hoistTo(treeCopy, [treeCopy], new Set([treeCopy.locator]), new Map(), options).isGraphChanged\n    if (isGraphChanged) throw new Error(`The hoisting result is not terminal, prev tree:\\n${prevTreeDump}, next tree:\\n${dumpDepTree(treeCopy)}`)\n    const checkLog = selfCheck(treeCopy)\n    if (checkLog) {\n      throw new Error(`${checkLog}, after hoisting finished:\\n${dumpDepTree(treeCopy)}`)\n    }\n  }\n\n  if (options.debugLevel >= DebugLevel.REASONS) console.log(dumpDepTree(treeCopy))\n\n  return shrinkTree(treeCopy)\n}\n\nconst getZeroRoundUsedDependencies = (rootNodePath: Array<HoisterWorkTree>): Map<PackageName, HoisterWorkTree> => {\n  const rootNode = rootNodePath[rootNodePath.length - 1]\n  const usedDependencies = new Map()\n  const seenNodes = new Set<HoisterWorkTree>()\n\n  const addUsedDependencies = (node: HoisterWorkTree) => {\n    if (seenNodes.has(node)) return\n    seenNodes.add(node)\n\n    for (const dep of node.hoistedDependencies.values()) usedDependencies.set(dep.name, dep)\n\n    for (const dep of node.dependencies.values()) {\n      if (!node.peerNames.has(dep.name)) {\n        addUsedDependencies(dep)\n      }\n    }\n  }\n\n  addUsedDependencies(rootNode)\n\n  return usedDependencies\n}\n\nconst getUsedDependencies = (rootNodePath: Array<HoisterWorkTree>): Map<PackageName, HoisterWorkTree> => {\n  const rootNode = rootNodePath[rootNodePath.length - 1]\n  const usedDependencies = new Map()\n  const seenNodes = new Set<HoisterWorkTree>()\n\n  const hiddenDependencies = new Set<PackageName>()\n  const addUsedDependencies = (node: HoisterWorkTree, hiddenDependencies: Set<PackageName>) => {\n    if (seenNodes.has(node)) return\n    seenNodes.add(node)\n\n    for (const dep of node.hoistedDependencies.values()) {\n      if (!hiddenDependencies.has(dep.name)) {\n        let reachableDependency\n        for (const node of rootNodePath) {\n          reachableDependency = node.dependencies.get(dep.name)\n          if (reachableDependency) {\n            usedDependencies.set(reachableDependency.name, reachableDependency)\n          }\n        }\n      }\n    }\n\n    const childrenHiddenDependencies = new Set<PackageName>()\n\n    for (const dep of node.dependencies.values()) childrenHiddenDependencies.add(dep.name)\n\n    for (const dep of node.dependencies.values()) {\n      if (!node.peerNames.has(dep.name)) {\n        addUsedDependencies(dep, childrenHiddenDependencies)\n      }\n    }\n  }\n\n  addUsedDependencies(rootNode, hiddenDependencies)\n\n  return usedDependencies\n}\n\n/**\n * This method clones the node and returns cloned node copy, if the node was not previously decoupled.\n *\n * The node is considered decoupled if there is no multiple parents to any node\n * on the path from the dependency graph root up to this node. This means that there are no other\n * nodes in dependency graph that somehow transitively use this node and hence node can be hoisted without\n * side effects.\n *\n * The process of node decoupling is done by going from root node of the graph up to the node in concern\n * and decoupling each node on this graph path.\n *\n * @param node original node\n *\n * @returns decoupled node\n */\nconst decoupleGraphNode = (parent: HoisterWorkTree, node: HoisterWorkTree): HoisterWorkTree => {\n  if (node.decoupled) return node\n\n  const {\n    name,\n    references,\n    ident,\n    locator,\n    dependencies,\n    originalDependencies,\n    hoistedDependencies,\n    peerNames,\n    reasons,\n    isHoistBorder,\n    hoistPriority,\n    dependencyKind,\n    hoistedFrom,\n    hoistedTo,\n  } = node\n  // To perform node hoisting from parent node we must clone parent nodes up to the root node,\n  // because some other package in the tree might depend on the parent package where hoisting\n  // cannot be performed\n  const clone = {\n    name,\n    references: new Set(references),\n    ident,\n    locator,\n    dependencies: new Map(dependencies),\n    originalDependencies: new Map(originalDependencies),\n    hoistedDependencies: new Map(hoistedDependencies),\n    peerNames: new Set(peerNames),\n    reasons: new Map(reasons),\n    decoupled: true,\n    isHoistBorder,\n    hoistPriority,\n    dependencyKind,\n    hoistedFrom: new Map(hoistedFrom),\n    hoistedTo: new Map(hoistedTo),\n  }\n  const selfDep = clone.dependencies.get(name)\n  if (selfDep && selfDep.ident == clone.ident)\n    // Update self-reference\n    clone.dependencies.set(name, clone)\n\n  parent.dependencies.set(clone.name, clone)\n\n  return clone\n}\n\n/**\n * Builds a map of most preferred packages that might be hoisted to the root node.\n *\n * The values in the map are idents sorted by preference from most preferred to less preferred.\n * If the root node has already some version of a package, the value array will contain only\n * one element, since it is not possible for other versions of a package to be hoisted.\n *\n * @param rootNode root node\n * @param preferenceMap preference map\n */\nconst getHoistIdentMap = (rootNode: HoisterWorkTree, preferenceMap: PreferenceMap): Map<PackageName, Array<Ident>> => {\n  const identMap = new Map<PackageName, Array<Ident>>([[rootNode.name, [rootNode.ident]]])\n\n  for (const dep of rootNode.dependencies.values()) {\n    if (!rootNode.peerNames.has(dep.name)) {\n      identMap.set(dep.name, [dep.ident])\n    }\n  }\n\n  const keyList = Array.from(preferenceMap.keys())\n  keyList.sort((key1, key2) => {\n    const entry1 = preferenceMap.get(key1)!\n    const entry2 = preferenceMap.get(key2)!\n    if (entry2.hoistPriority !== entry1.hoistPriority) {\n      return entry2.hoistPriority - entry1.hoistPriority\n    } else {\n      const entry1Usages = entry1.dependents.size + entry1.peerDependents.size\n      const entry2Usages = entry2.dependents.size + entry2.peerDependents.size\n      return entry2Usages - entry1Usages\n    }\n  })\n\n  for (const key of keyList) {\n    const name = key.substring(0, key.indexOf(`@`, 1))\n    const ident = key.substring(name.length + 1)\n    if (!rootNode.peerNames.has(name)) {\n      let idents = identMap.get(name)\n      if (!idents) {\n        idents = []\n        identMap.set(name, idents)\n      }\n      if (idents.indexOf(ident) < 0) {\n        idents.push(ident)\n      }\n    }\n  }\n\n  return identMap\n}\n\n/**\n * Gets regular node dependencies only and sorts them in the order so that\n * peer dependencies come before the dependency that rely on them.\n *\n * @param node graph node\n * @returns sorted regular dependencies\n */\nconst getSortedRegularDependencies = (node: HoisterWorkTree): Set<HoisterWorkTree> => {\n  const dependencies: Set<HoisterWorkTree> = new Set()\n\n  const addDep = (dep: HoisterWorkTree, seenDeps = new Set()) => {\n    if (seenDeps.has(dep)) return\n    seenDeps.add(dep)\n\n    for (const peerName of dep.peerNames) {\n      if (!node.peerNames.has(peerName)) {\n        const peerDep = node.dependencies.get(peerName)\n        if (peerDep && !dependencies.has(peerDep)) {\n          addDep(peerDep, seenDeps)\n        }\n      }\n    }\n    dependencies.add(dep)\n  }\n\n  for (const dep of node.dependencies.values()) {\n    if (!node.peerNames.has(dep.name)) {\n      addDep(dep)\n    }\n  }\n\n  return dependencies\n}\n\n/**\n * Performs hoisting all the dependencies down the tree to the root node.\n *\n * The algorithm used here reduces dependency graph by deduplicating\n * instances of the packages while keeping:\n * 1. Regular dependency promise: the package should require the exact version of the dependency\n * that was declared in its `package.json`\n * 2. Peer dependency promise: the package and its direct parent package\n * must use the same instance of the peer dependency\n *\n * The regular and peer dependency promises are kept while performing transform\n * on tree branches of packages at a time:\n * `root package` -> `parent package 1` ... `parent package n` -> `dependency`\n * We check wether we can hoist `dependency` to `root package`, this boils down basically\n * to checking:\n * 1. Wether `root package` does not depend on other version of `dependency`\n * 2. Wether all the peer dependencies of a `dependency` had already been hoisted from all `parent packages`\n *\n * If many versions of the `dependency` can be hoisted to the `root package` we choose the most used\n * `dependency` version in the project among them.\n *\n * This function mutates the tree.\n *\n * @param tree package dependencies graph\n * @param rootNode root node to hoist to\n * @param rootNodePath root node path in the tree\n * @param rootNodePathLocators a set of locators for nodes that lead from the top of the tree up to root node\n * @param options hoisting options\n */\nconst hoistTo = (\n  tree: HoisterWorkTree,\n  rootNodePath: Array<HoisterWorkTree>,\n  rootNodePathLocators: Set<Locator>,\n  parentShadowedNodes: ShadowedNodes,\n  options: InternalHoistOptions,\n  seenNodes: Set<HoisterWorkTree> = new Set()\n): { anotherRoundNeeded: boolean; isGraphChanged: boolean } => {\n  const rootNode = rootNodePath[rootNodePath.length - 1]\n  if (seenNodes.has(rootNode)) return { anotherRoundNeeded: false, isGraphChanged: false }\n  seenNodes.add(rootNode)\n\n  const preferenceMap = buildPreferenceMap(rootNode)\n\n  const hoistIdentMap = getHoistIdentMap(rootNode, preferenceMap)\n\n  const usedDependencies = tree == rootNode ? new Map() : options.fastLookupPossible ? getZeroRoundUsedDependencies(rootNodePath) : getUsedDependencies(rootNodePath)\n\n  let wasStateChanged\n\n  let anotherRoundNeeded = false\n  let isGraphChanged = false\n\n  const hoistIdents = new Map(Array.from(hoistIdentMap.entries()).map(([k, v]) => [k, v[0]]))\n  const shadowedNodes: ShadowedNodes = new Map()\n  do {\n    const result = hoistGraph(tree, rootNodePath, rootNodePathLocators, usedDependencies, hoistIdents, hoistIdentMap, parentShadowedNodes, shadowedNodes, options)\n    if (result.isGraphChanged) isGraphChanged = true\n    if (result.anotherRoundNeeded) anotherRoundNeeded = true\n\n    wasStateChanged = false\n    for (const [name, idents] of hoistIdentMap) {\n      if (idents.length > 1 && !rootNode.dependencies.has(name)) {\n        hoistIdents.delete(name)\n        idents.shift()\n        hoistIdents.set(name, idents[0])\n        wasStateChanged = true\n      }\n    }\n  } while (wasStateChanged)\n\n  for (const dependency of rootNode.dependencies.values()) {\n    if (!rootNode.peerNames.has(dependency.name) && !rootNodePathLocators.has(dependency.locator)) {\n      rootNodePathLocators.add(dependency.locator)\n      const result = hoistTo(tree, [...rootNodePath, dependency], rootNodePathLocators, shadowedNodes, options)\n      if (result.isGraphChanged) isGraphChanged = true\n      if (result.anotherRoundNeeded) anotherRoundNeeded = true\n\n      rootNodePathLocators.delete(dependency.locator)\n    }\n  }\n\n  return { anotherRoundNeeded, isGraphChanged }\n}\n\nconst hasUnhoistedDependencies = (node: HoisterWorkTree): boolean => {\n  for (const [subName, subDependency] of node.dependencies) {\n    if (!node.peerNames.has(subName) && subDependency.ident !== node.ident) {\n      return true\n    }\n  }\n  return false\n}\n\nconst getNodeHoistInfo = (\n  rootNode: HoisterWorkTree,\n  rootNodePathLocators: Set<Locator>,\n  nodePath: Array<HoisterWorkTree>,\n  node: HoisterWorkTree,\n  usedDependencies: Map<PackageName, HoisterWorkTree>,\n  hoistIdents: Map<PackageName, Ident>,\n  hoistIdentMap: Map<Ident, Array<Ident>>,\n  shadowedNodes: ShadowedNodes,\n  { outputReason, fastLookupPossible }: { outputReason: boolean; fastLookupPossible: boolean }\n): HoistInfo => {\n  let reasonRoot\n  let reason: string | null = null\n  let dependsOn: Set<HoisterWorkTree> | null = new Set()\n  if (outputReason)\n    reasonRoot = `${Array.from(rootNodePathLocators)\n      .map(x => prettyPrintLocator(x))\n      .join(`→`)}`\n\n  const parentNode = nodePath[nodePath.length - 1]\n  // We cannot hoist self-references\n  const isSelfReference = node.ident === parentNode.ident\n  let isHoistable = !isSelfReference\n  if (outputReason && !isHoistable) reason = `- self-reference`\n\n  if (isHoistable) {\n    isHoistable = node.dependencyKind !== HoisterDependencyKind.WORKSPACE\n    if (outputReason && !isHoistable) {\n      reason = `- workspace`\n    }\n  }\n\n  if (isHoistable && node.dependencyKind === HoisterDependencyKind.EXTERNAL_SOFT_LINK) {\n    isHoistable = !hasUnhoistedDependencies(node)\n    if (outputReason && !isHoistable) {\n      reason = `- external soft link with unhoisted dependencies`\n    }\n  }\n\n  if (isHoistable) {\n    isHoistable = !rootNode.peerNames.has(node.name)\n    if (outputReason && !isHoistable) {\n      reason = `- cannot shadow peer: ${prettyPrintLocator(rootNode.originalDependencies.get(node.name)!.locator)} at ${reasonRoot}`\n    }\n  }\n\n  if (isHoistable) {\n    let isNameAvailable = false\n    const usedDep = usedDependencies.get(node.name)\n    isNameAvailable = !usedDep || usedDep.ident === node.ident\n    if (outputReason && !isNameAvailable) reason = `- filled by: ${prettyPrintLocator(usedDep!.locator)} at ${reasonRoot}`\n    if (isNameAvailable) {\n      for (let idx = nodePath.length - 1; idx >= 1; idx--) {\n        const parent = nodePath[idx]\n        const parentDep = parent.dependencies.get(node.name)\n        if (parentDep && parentDep.ident !== node.ident) {\n          isNameAvailable = false\n          let shadowedNames = shadowedNodes.get(parentNode)\n          if (!shadowedNames) {\n            shadowedNames = new Set()\n            shadowedNodes.set(parentNode, shadowedNames)\n          }\n          shadowedNames.add(node.name)\n          if (outputReason)\n            reason = `- filled by ${prettyPrintLocator(parentDep.locator)} at ${nodePath\n              .slice(0, idx)\n              .map(x => prettyPrintLocator(x.locator))\n              .join(`→`)}`\n          break\n        }\n      }\n    }\n\n    isHoistable = isNameAvailable\n  }\n\n  if (isHoistable) {\n    const hoistedIdent = hoistIdents.get(node.name)\n    isHoistable = hoistedIdent === node.ident\n    if (outputReason && !isHoistable) {\n      reason = `- filled by: ${prettyPrintLocator(hoistIdentMap.get(node.name)![0])} at ${reasonRoot}`\n    }\n  }\n\n  if (isHoistable) {\n    let arePeerDepsSatisfied = true\n    const checkList = new Set(node.peerNames)\n    for (let idx = nodePath.length - 1; idx >= 1; idx--) {\n      const parent = nodePath[idx]\n      for (const name of checkList) {\n        if (parent.peerNames.has(name) && parent.originalDependencies.has(name)) continue\n\n        const parentDepNode = parent.dependencies.get(name)\n        if (parentDepNode && rootNode.dependencies.get(name) !== parentDepNode) {\n          if (idx === nodePath.length - 1) {\n            dependsOn!.add(parentDepNode)\n          } else {\n            dependsOn = null\n            arePeerDepsSatisfied = false\n            if (outputReason) {\n              reason = `- peer dependency ${prettyPrintLocator(parentDepNode.locator)} from parent ${prettyPrintLocator(parent.locator)} was not hoisted to ${reasonRoot}`\n            }\n          }\n        }\n        checkList.delete(name)\n      }\n      if (!arePeerDepsSatisfied) {\n        break\n      }\n    }\n    isHoistable = arePeerDepsSatisfied\n  }\n\n  if (isHoistable && !fastLookupPossible) {\n    for (const origDep of node.hoistedDependencies.values()) {\n      const usedDep = usedDependencies.get(origDep.name) || rootNode.dependencies.get(origDep.name)\n      if (!usedDep || origDep.ident !== usedDep.ident) {\n        isHoistable = false\n        if (outputReason) reason = `- previously hoisted dependency mismatch, needed: ${prettyPrintLocator(origDep.locator)}, available: ${prettyPrintLocator(usedDep?.locator)}`\n\n        break\n      }\n    }\n  }\n\n  if (dependsOn !== null && dependsOn.size > 0) {\n    return { isHoistable: Hoistable.DEPENDS, dependsOn, reason }\n  } else {\n    return { isHoistable: isHoistable ? Hoistable.YES : Hoistable.NO, reason }\n  }\n}\n\nconst getAliasedLocator = (node: HoisterWorkTree): AliasedLocator => `${node.name}@${node.locator}` as AliasedLocator\n\n/**\n * Performs actual graph transformation, by hoisting packages to the root node.\n *\n * @param tree dependency tree\n * @param rootNodePath root node path in the tree\n * @param rootNodePathLocators a set of locators for nodes that lead from the top of the tree up to root node\n * @param usedDependencies map of dependency nodes from parents of root node used by root node and its children via parent lookup\n * @param hoistIdents idents that should be attempted to be hoisted to the root node\n */\nconst hoistGraph = (\n  tree: HoisterWorkTree,\n  rootNodePath: Array<HoisterWorkTree>,\n  rootNodePathLocators: Set<Locator>,\n  usedDependencies: Map<PackageName, HoisterWorkTree>,\n  hoistIdents: Map<PackageName, Ident>,\n  hoistIdentMap: Map<Ident, Array<Ident>>,\n  parentShadowedNodes: ShadowedNodes,\n  shadowedNodes: ShadowedNodes,\n  options: InternalHoistOptions\n): { anotherRoundNeeded: boolean; isGraphChanged: boolean } => {\n  const rootNode = rootNodePath[rootNodePath.length - 1]\n  const seenNodes = new Set<HoisterWorkTree>()\n  let anotherRoundNeeded = false\n  let isGraphChanged = false\n\n  const hoistNodeDependencies = (\n    nodePath: Array<HoisterWorkTree>,\n    locatorPath: Array<Locator>,\n    aliasedLocatorPath: Array<AliasedLocator>,\n    parentNode: HoisterWorkTree,\n    newNodes: Set<HoisterWorkTree>\n  ) => {\n    if (seenNodes.has(parentNode)) return\n    const nextLocatorPath = [...locatorPath, getAliasedLocator(parentNode)]\n    const nextAliasedLocatorPath = [...aliasedLocatorPath, getAliasedLocator(parentNode)]\n\n    const dependantTree = new Map<PackageName, Set<PackageName>>()\n    const hoistInfos = new Map<HoisterWorkTree, HoistInfo>()\n    for (const subDependency of getSortedRegularDependencies(parentNode)) {\n      const hoistInfo = getNodeHoistInfo(\n        rootNode,\n        rootNodePathLocators,\n        [rootNode, ...nodePath, parentNode],\n        subDependency,\n        usedDependencies,\n        hoistIdents,\n        hoistIdentMap,\n        shadowedNodes,\n        { outputReason: options.debugLevel >= DebugLevel.REASONS, fastLookupPossible: options.fastLookupPossible }\n      )\n\n      hoistInfos.set(subDependency, hoistInfo)\n      if (hoistInfo.isHoistable === Hoistable.DEPENDS) {\n        for (const node of hoistInfo.dependsOn) {\n          const nodeDependants = dependantTree.get(node.name) || new Set()\n          nodeDependants.add(subDependency.name)\n          dependantTree.set(node.name, nodeDependants)\n        }\n      }\n    }\n\n    const unhoistableNodes = new Set<HoisterWorkTree>()\n    const addUnhoistableNode = (node: HoisterWorkTree, hoistInfo: HoistInfo, reason: string) => {\n      if (!unhoistableNodes.has(node)) {\n        unhoistableNodes.add(node)\n        hoistInfos.set(node, { isHoistable: Hoistable.NO, reason })\n        for (const dependantName of dependantTree.get(node.name) || []) {\n          addUnhoistableNode(\n            parentNode.dependencies.get(dependantName)!,\n            hoistInfo,\n            options.debugLevel >= DebugLevel.REASONS\n              ? `- peer dependency ${prettyPrintLocator(node.locator)} from parent ${prettyPrintLocator(parentNode.locator)} was not hoisted`\n              : ``\n          )\n        }\n      }\n    }\n\n    for (const [node, hoistInfo] of hoistInfos) if (hoistInfo.isHoistable === Hoistable.NO) addUnhoistableNode(node, hoistInfo, hoistInfo.reason!)\n\n    let wereNodesHoisted = false\n    for (const node of hoistInfos.keys()) {\n      if (!unhoistableNodes.has(node)) {\n        isGraphChanged = true\n        const shadowedNames = parentShadowedNodes.get(parentNode)\n        if (shadowedNames && shadowedNames.has(node.name)) anotherRoundNeeded = true\n\n        wereNodesHoisted = true\n        parentNode.dependencies.delete(node.name)\n        parentNode.hoistedDependencies.set(node.name, node)\n        parentNode.reasons.delete(node.name)\n\n        const hoistedNode = rootNode.dependencies.get(node.name)\n        if (options.debugLevel >= DebugLevel.REASONS) {\n          const hoistedFrom = Array.from(locatorPath)\n            .concat([parentNode.locator])\n            .map(x => prettyPrintLocator(x))\n            .join(`→`)\n          let hoistedFromArray = rootNode.hoistedFrom.get(node.name)\n          if (!hoistedFromArray) {\n            hoistedFromArray = []\n            rootNode.hoistedFrom.set(node.name, hoistedFromArray)\n          }\n          hoistedFromArray.push(hoistedFrom)\n\n          parentNode.hoistedTo.set(\n            node.name,\n            Array.from(rootNodePath)\n              .map(x => prettyPrintLocator(x.locator))\n              .join(`→`)\n          )\n        }\n        // Add hoisted node to root node, in case it is not already there\n        if (!hoistedNode) {\n          // Avoid adding other version of root node to itself\n          if (rootNode.ident !== node.ident) {\n            rootNode.dependencies.set(node.name, node)\n            newNodes.add(node)\n          }\n        } else {\n          for (const reference of node.references) {\n            hoistedNode.references.add(reference)\n          }\n        }\n      }\n    }\n\n    if (parentNode.dependencyKind === HoisterDependencyKind.EXTERNAL_SOFT_LINK && wereNodesHoisted) anotherRoundNeeded = true\n\n    if (options.check) {\n      const checkLog = selfCheck(tree)\n      if (checkLog) {\n        throw new Error(\n          `${checkLog}, after hoisting dependencies of ${[rootNode, ...nodePath, parentNode].map(x => prettyPrintLocator(x.locator)).join(`→`)}:\\n${dumpDepTree(tree)}`\n        )\n      }\n    }\n\n    const children = getSortedRegularDependencies(parentNode)\n    for (const node of children) {\n      if (unhoistableNodes.has(node)) {\n        const hoistInfo = hoistInfos.get(node)!\n        const hoistableIdent = hoistIdents.get(node.name)\n        if ((hoistableIdent === node.ident || !parentNode.reasons.has(node.name)) && hoistInfo.isHoistable !== Hoistable.YES) parentNode.reasons.set(node.name, hoistInfo.reason!)\n\n        if (!node.isHoistBorder && nextAliasedLocatorPath.indexOf(getAliasedLocator(node)) < 0) {\n          seenNodes.add(parentNode)\n          const decoupledNode = decoupleGraphNode(parentNode, node)\n\n          hoistNodeDependencies([...nodePath, parentNode], nextLocatorPath, nextAliasedLocatorPath, decoupledNode, nextNewNodes)\n\n          seenNodes.delete(parentNode)\n        }\n      }\n    }\n  }\n\n  let newNodes\n  let nextNewNodes = new Set(getSortedRegularDependencies(rootNode))\n  const aliasedRootNodePathLocators = Array.from(rootNodePath).map(x => getAliasedLocator(x))\n  do {\n    newNodes = nextNewNodes\n    nextNewNodes = new Set()\n    for (const dep of newNodes) {\n      if (dep.locator === rootNode.locator || dep.isHoistBorder) continue\n      const decoupledDependency = decoupleGraphNode(rootNode, dep)\n\n      hoistNodeDependencies([], Array.from(rootNodePathLocators), aliasedRootNodePathLocators, decoupledDependency, nextNewNodes)\n    }\n  } while (nextNewNodes.size > 0)\n\n  return { anotherRoundNeeded, isGraphChanged }\n}\n\nconst selfCheck = (tree: HoisterWorkTree): string => {\n  const log: Array<string> = []\n\n  const seenNodes = new Set()\n  const parents = new Set<HoisterWorkTree>()\n\n  const checkNode = (node: HoisterWorkTree, parentDeps: Map<PackageName, HoisterWorkTree>, parent: HoisterWorkTree) => {\n    if (seenNodes.has(node)) return\n    seenNodes.add(node)\n\n    if (parents.has(node)) return\n\n    const dependencies = new Map(parentDeps)\n    for (const dep of node.dependencies.values()) if (!node.peerNames.has(dep.name)) dependencies.set(dep.name, dep)\n\n    for (const origDep of node.originalDependencies.values()) {\n      const dep = dependencies.get(origDep.name)\n      const prettyPrintTreePath = () =>\n        `${Array.from(parents)\n          .concat([node])\n          .map(x => prettyPrintLocator(x.locator))\n          .join(`→`)}`\n      if (node.peerNames.has(origDep.name)) {\n        const parentDep = parentDeps.get(origDep.name)\n        if (parentDep !== dep || !parentDep || parentDep.ident !== origDep.ident) {\n          log.push(`${prettyPrintTreePath()} - broken peer promise: expected ${origDep.ident} but found ${parentDep ? parentDep.ident : parentDep}`)\n        }\n      } else {\n        const hoistedFrom = parent.hoistedFrom.get(node.name)\n        const originalHoistedTo = node.hoistedTo.get(origDep.name)\n        const prettyHoistedFrom = `${hoistedFrom ? ` hoisted from ${hoistedFrom.join(`, `)}` : ``}`\n        const prettyOriginalHoistedTo = `${originalHoistedTo ? ` hoisted to ${originalHoistedTo}` : ``}`\n        const prettyNodePath = `${prettyPrintTreePath()}${prettyHoistedFrom}`\n        if (!dep) {\n          log.push(`${prettyNodePath} - broken require promise: no required dependency ${origDep.name}${prettyOriginalHoistedTo} found`)\n        } else if (dep.ident !== origDep.ident) {\n          log.push(`${prettyNodePath} - broken require promise for ${origDep.name}${prettyOriginalHoistedTo}: expected ${origDep.ident}, but found: ${dep.ident}`)\n        }\n      }\n    }\n\n    parents.add(node)\n    for (const dep of node.dependencies.values()) {\n      if (!node.peerNames.has(dep.name)) {\n        checkNode(dep, dependencies, node)\n      }\n    }\n    parents.delete(node)\n  }\n\n  checkNode(tree, tree.dependencies, tree)\n\n  return log.join(`\\n`)\n}\n\n/**\n * Creates a clone of package tree with extra fields used for hoisting purposes.\n *\n * @param tree package tree clone\n */\nconst cloneTree = (tree: HoisterTree, options: InternalHoistOptions): HoisterWorkTree => {\n  const { identName, name, reference, peerNames } = tree\n  const treeCopy: HoisterWorkTree = {\n    name,\n    references: new Set([reference]),\n    locator: makeLocator(identName, reference),\n    ident: makeIdent(identName, reference),\n    dependencies: new Map(),\n    originalDependencies: new Map(),\n    hoistedDependencies: new Map(),\n    peerNames: new Set(peerNames),\n    reasons: new Map(),\n    decoupled: true,\n    isHoistBorder: true,\n    hoistPriority: 0,\n    dependencyKind: HoisterDependencyKind.WORKSPACE,\n    hoistedFrom: new Map(),\n    hoistedTo: new Map(),\n  }\n\n  const seenNodes = new Map<HoisterTree, HoisterWorkTree>([[tree, treeCopy]])\n\n  const addNode = (node: HoisterTree, parentNode: HoisterWorkTree) => {\n    let workNode = seenNodes.get(node)\n    const isSeen = !!workNode\n    if (!workNode) {\n      const { name, identName, reference, peerNames, hoistPriority, dependencyKind } = node\n      const dependenciesNmHoistingLimits = options.hoistingLimits.get(parentNode.locator)\n      workNode = {\n        name,\n        references: new Set([reference]),\n        locator: makeLocator(identName, reference),\n        ident: makeIdent(identName, reference),\n        dependencies: new Map(),\n        originalDependencies: new Map(),\n        hoistedDependencies: new Map(),\n        peerNames: new Set(peerNames),\n        reasons: new Map(),\n        decoupled: true,\n        isHoistBorder: dependenciesNmHoistingLimits ? dependenciesNmHoistingLimits.has(name) : false,\n        hoistPriority: hoistPriority || 0,\n        dependencyKind: dependencyKind || HoisterDependencyKind.REGULAR,\n        hoistedFrom: new Map(),\n        hoistedTo: new Map(),\n      }\n      seenNodes.set(node, workNode)\n    }\n\n    parentNode.dependencies.set(node.name, workNode)\n    parentNode.originalDependencies.set(node.name, workNode)\n\n    if (!isSeen) {\n      for (const dep of node.dependencies) {\n        addNode(dep, workNode)\n      }\n    } else {\n      const seenCoupledNodes = new Set()\n\n      const markNodeCoupled = (node: HoisterWorkTree) => {\n        if (seenCoupledNodes.has(node)) return\n        seenCoupledNodes.add(node)\n        node.decoupled = false\n\n        for (const dep of node.dependencies.values()) {\n          if (!node.peerNames.has(dep.name)) {\n            markNodeCoupled(dep)\n          }\n        }\n      }\n\n      markNodeCoupled(workNode)\n    }\n  }\n\n  for (const dep of tree.dependencies) addNode(dep, treeCopy)\n\n  return treeCopy\n}\n\nconst getIdentName = (locator: Locator) => locator.substring(0, locator.indexOf(`@`, 1))\n\n/**\n * Creates a clone of hoisted package tree with extra fields removed\n *\n * @param tree stripped down hoisted package tree clone\n */\nconst shrinkTree = (tree: HoisterWorkTree): HoisterResult => {\n  const treeCopy: HoisterResult = {\n    name: tree.name,\n    identName: getIdentName(tree.locator),\n    references: new Set(tree.references),\n    dependencies: new Set(),\n  }\n\n  const seenNodes = new Set<HoisterWorkTree>([tree])\n\n  const addNode = (node: HoisterWorkTree, parentWorkNode: HoisterWorkTree, parentNode: HoisterResult) => {\n    const isSeen = seenNodes.has(node)\n\n    let resultNode: HoisterResult\n    if (parentWorkNode === node) {\n      resultNode = parentNode\n    } else {\n      const { name, references, locator } = node\n      resultNode = {\n        name,\n        identName: getIdentName(locator),\n        references,\n        dependencies: new Set<HoisterResult>(),\n      }\n    }\n    parentNode.dependencies.add(resultNode)\n\n    if (!isSeen) {\n      seenNodes.add(node)\n      for (const dep of node.dependencies.values()) {\n        if (!node.peerNames.has(dep.name)) {\n          addNode(dep, node, resultNode)\n        }\n      }\n      seenNodes.delete(node)\n    }\n  }\n\n  for (const dep of tree.dependencies.values()) addNode(dep, tree, treeCopy)\n\n  return treeCopy\n}\n\n/**\n * Builds mapping, where key is an alias + dependent package ident and the value is the list of\n * parent package idents who depend on this package.\n *\n * @param rootNode package tree root node\n *\n * @returns preference map\n */\nconst buildPreferenceMap = (rootNode: HoisterWorkTree): PreferenceMap => {\n  const preferenceMap: PreferenceMap = new Map()\n\n  const seenNodes = new Set<HoisterWorkTree>([rootNode])\n  const getPreferenceKey = (node: HoisterWorkTree) => `${node.name}@${node.ident}`\n\n  const getOrCreatePreferenceEntry = (node: HoisterWorkTree) => {\n    const key = getPreferenceKey(node)\n    let entry = preferenceMap.get(key)\n    if (!entry) {\n      entry = { dependents: new Set<Ident>(), peerDependents: new Set<Ident>(), hoistPriority: 0 }\n      preferenceMap.set(key, entry)\n    }\n    return entry\n  }\n\n  const addDependent = (dependent: HoisterWorkTree, node: HoisterWorkTree) => {\n    const isSeen = !!seenNodes.has(node)\n\n    const entry = getOrCreatePreferenceEntry(node)\n    entry.dependents.add(dependent.ident)\n\n    if (!isSeen) {\n      seenNodes.add(node)\n      for (const dep of node.dependencies.values()) {\n        const entry = getOrCreatePreferenceEntry(dep)\n        entry.hoistPriority = Math.max(entry.hoistPriority, dep.hoistPriority)\n        if (node.peerNames.has(dep.name)) {\n          entry.peerDependents.add(node.ident)\n        } else {\n          addDependent(node, dep)\n        }\n      }\n    }\n  }\n\n  for (const dep of rootNode.dependencies.values()) if (!rootNode.peerNames.has(dep.name)) addDependent(rootNode, dep)\n\n  return preferenceMap\n}\n\nconst prettyPrintLocator = (locator?: Locator) => {\n  if (!locator) return `none`\n\n  const idx = locator.indexOf(`@`, 1)\n  let name = locator.substring(0, idx)\n  if (name.endsWith(`$wsroot$`)) name = `wh:${name.replace(`$wsroot$`, ``)}`\n  const reference = locator.substring(idx + 1)\n  if (reference === `workspace:.`) {\n    return `.`\n  } else if (!reference) {\n    return `${name}`\n  } else {\n    let version = (reference.indexOf(`#`) > 0 ? reference.split(`#`)[1] : reference).replace(`npm:`, ``)\n    if (reference.startsWith(`virtual`)) name = `v:${name}`\n    if (version.startsWith(`workspace`)) {\n      name = `w:${name}`\n      version = ``\n    }\n\n    return `${name}${version ? `@${version}` : ``}`\n  }\n}\n\nconst MAX_NODES_TO_DUMP = 50000\n\n/**\n * Pretty-prints dependency tree in the `yarn why`-like format\n *\n * The function is used for troubleshooting purposes only.\n *\n * @param pkg node_modules tree\n *\n * @returns sorted node_modules tree\n */\n\nconst dumpDepTree = (tree: HoisterWorkTree) => {\n  let nodeCount = 0\n  const dumpPackage = (pkg: HoisterWorkTree, parents: Set<HoisterWorkTree>, prefix = ``): string => {\n    if (nodeCount > MAX_NODES_TO_DUMP || parents.has(pkg)) return ``\n\n    nodeCount++\n    const dependencies = Array.from(pkg.dependencies.values()).sort((n1, n2) => {\n      if (n1.name === n2.name) {\n        return 0\n      } else {\n        return n1.name > n2.name ? 1 : -1\n      }\n    })\n\n    let str = ``\n    parents.add(pkg)\n    for (let idx = 0; idx < dependencies.length; idx++) {\n      const dep = dependencies[idx]\n      if (!pkg.peerNames.has(dep.name) && dep !== pkg) {\n        const reason = pkg.reasons.get(dep.name)\n        const identName = getIdentName(dep.locator)\n        str += `${prefix}${idx < dependencies.length - 1 ? `├─` : `└─`}${(parents.has(dep) ? `>` : ``) + (identName !== dep.name ? `a:${dep.name}:` : ``) + prettyPrintLocator(dep.locator) + (reason ? ` ${reason}` : ``)}\\n`\n        str += dumpPackage(dep, parents, `${prefix}${idx < dependencies.length - 1 ? `│ ` : `  `}`)\n      }\n    }\n    parents.delete(pkg)\n    return str\n  }\n\n  const treeDump = dumpPackage(tree, new Set())\n\n  return treeDump + (nodeCount > MAX_NODES_TO_DUMP ? `\\nTree is too large, part of the tree has been dunped\\n` : ``)\n}\n"]}