{"version": 3, "file": "mapper.js", "sourceRoot": "", "sources": ["../src/mapper.ts"], "names": [], "mappings": ";;AAEA,kCAaC;AAED,gCAMC;AArBD,SAAgB,WAAW,CAAC,GAAiB;IAC3C,MAAM,GAAG,GAAQ,EAAE,CAAA;IACnB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACrB,SAAQ;QACV,CAAC;QACD,IAAI,KAAK,YAAY,GAAG,EAAE,CAAC;YACzB,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAA;QAC/B,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;QAClB,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,SAAgB,UAAU,CAAC,GAAQ;IACjC,MAAM,mBAAmB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,CAAA;IACrE,IAAI,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACtC,OAAO,KAAK,CAAA;IACd,CAAC;IACD,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,OAAO,GAAG,CAAC,IAAI,GAAG,KAAK,IAAI,CAAA;AACvF,CAAC", "sourcesContent": ["type RecursiveMap = Map<any, RecursiveMap | any>\n\nexport function mapToObject(map: RecursiveMap) {\n  const obj: any = {}\n  for (const [key, value] of map) {\n    if (!is<PERSON>alid<PERSON><PERSON>(key)) {\n      continue\n    }\n    if (value instanceof Map) {\n      obj[key] = mapToObject(value)\n    } else {\n      obj[key] = value\n    }\n  }\n  return obj\n}\n\nexport function isValid<PERSON>ey(key: any) {\n  const protectedProperties = [\"__proto__\", \"prototype\", \"constructor\"]\n  if (protectedProperties.includes(key)) {\n    return false\n  }\n  return [\"string\", \"number\", \"symbol\", \"boolean\"].includes(typeof key) || key === null\n}\n"]}