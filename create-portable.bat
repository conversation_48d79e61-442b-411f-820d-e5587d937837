@echo off
echo ========================================
echo    Hookah Ads - Creating Portable Package
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Creating portable package directory...
if exist "portable-package" rmdir /s /q "portable-package"
mkdir "portable-package"
mkdir "portable-package\HookahAds"

echo.
echo Copying application files...
copy "main.js" "portable-package\HookahAds\"
copy "banner.html" "portable-package\HookahAds\"
copy "control.html" "portable-package\HookahAds\"
copy "package.json" "portable-package\HookahAds\"
copy "README.md" "portable-package\HookahAds\"

echo.
echo Copying node_modules...
xcopy "node_modules" "portable-package\HookahAds\node_modules" /E /I /Q

echo.
echo Creating startup scripts...

:: Create Windows batch file
echo @echo off > "portable-package\HookahAds\start.bat"
echo echo Starting Hookah Ads... >> "portable-package\HookahAds\start.bat"
echo npm start >> "portable-package\HookahAds\start.bat"

:: Create PowerShell script
echo Write-Host "Starting Hookah Ads..." -ForegroundColor Green > "portable-package\HookahAds\start.ps1"
echo npm start >> "portable-package\HookahAds\start.ps1"

:: Create instructions
echo HOOKAH ADS - PORTABLE VERSION > "portable-package\HookahAds\INSTRUCTIONS.txt"
echo. >> "portable-package\HookahAds\INSTRUCTIONS.txt"
echo To run the application: >> "portable-package\HookahAds\INSTRUCTIONS.txt"
echo 1. Double-click "start.bat" >> "portable-package\HookahAds\INSTRUCTIONS.txt"
echo 2. OR open PowerShell here and run ".\start.ps1" >> "portable-package\HookahAds\INSTRUCTIONS.txt"
echo 3. OR open Command Prompt here and run "npm start" >> "portable-package\HookahAds\INSTRUCTIONS.txt"
echo. >> "portable-package\HookahAds\INSTRUCTIONS.txt"
echo Requirements: >> "portable-package\HookahAds\INSTRUCTIONS.txt"
echo - Node.js must be installed on the target computer >> "portable-package\HookahAds\INSTRUCTIONS.txt"
echo - Download from: https://nodejs.org/ >> "portable-package\HookahAds\INSTRUCTIONS.txt"

echo.
echo Creating ZIP archive...
if exist "HookahAds-Portable.zip" del "HookahAds-Portable.zip"
powershell -command "Compress-Archive -Path 'portable-package\HookahAds' -DestinationPath 'HookahAds-Portable.zip'"

echo.
echo ========================================
echo        PORTABLE PACKAGE CREATED!
echo ========================================
echo.
echo Files created:
echo - portable-package\HookahAds\ (folder with all files)
echo - HookahAds-Portable.zip (compressed archive)
echo.
echo To deploy:
echo 1. Copy the entire "HookahAds" folder to the target computer
echo 2. OR extract "HookahAds-Portable.zip" on the target computer
echo 3. Ensure Node.js is installed on the target computer
echo 4. Double-click "start.bat" to run the application
echo.
pause
