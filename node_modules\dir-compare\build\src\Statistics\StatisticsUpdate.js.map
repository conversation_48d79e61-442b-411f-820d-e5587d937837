{"version": 3, "file": "StatisticsUpdate.js", "sourceRoot": "", "sources": ["../../../src/Statistics/StatisticsUpdate.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACU,QAAA,gBAAgB,GAAG;IAC5B,oBAAoB,CAAC,MAAa,EAAE,MAAa,EAAE,IAAa,EAAE,MAAc,EAAE,IAAoB,EAClG,qBAA4C,EAAE,UAA6B,EAAE,OAAmB;QAEhG,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAA;QACjD,IAAI,IAAI,KAAK,MAAM,EAAE;YACjB,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,EAAE,CAAA;SAC9D;aAAM,IAAI,IAAI,KAAK,WAAW,EAAE;YAC7B,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,EAAE,CAAA;SAC5D;aAAM,IAAI,IAAI,KAAK,aAAa,EAAE;YAC/B,UAAU,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAA;SAC/C;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAA;SAC7C;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,MAAM,SAAS,GAAG,UAAU,IAAI,UAAU,CAAA;QAC1C,IAAI,OAAO,CAAC,cAAc,IAAI,SAAS,EAAE;YACrC,MAAM,iBAAiB,GAAG,UAAU,CAAC,QAA6B,CAAA;YAClE,IAAI,MAAM,KAAK,mBAAmB,EAAE;gBAChC,iBAAiB,CAAC,gBAAgB,EAAE,CAAA;aACvC;iBAAM;gBACH,iBAAiB,CAAC,aAAa,EAAE,CAAA;aACpC;SACJ;QAED,IAAI,qBAAqB,KAAK,mBAAmB,EAAE;YAC/C,UAAU,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAA;SACrD;aAAM,IAAI,qBAAqB,KAAK,oBAAoB,EAAE;YACvD,UAAU,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAA;SACtD;aAAM,IAAI,qBAAqB,KAAK,mBAAmB,EAAE;YACtD,UAAU,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,CAAA;SACzD;IACL,CAAC;IACD,oBAAoB,CAAC,MAAa,EAAE,IAAoB,EAAE,qBAA4C,EAClG,UAA6B,EAAE,OAAmB;QAElD,UAAU,CAAC,IAAI,EAAE,CAAA;QACjB,IAAI,IAAI,KAAK,MAAM,EAAE;YACjB,UAAU,CAAC,SAAS,EAAE,CAAA;SACzB;aAAM,IAAI,IAAI,KAAK,WAAW,EAAE;YAC7B,UAAU,CAAC,QAAQ,EAAE,CAAA;SACxB;aAAM,IAAI,IAAI,KAAK,aAAa,EAAE;YAC/B,UAAU,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;SAC3C;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAA;SAC7C;QAED,IAAI,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC,SAAS,EAAE;YAC5C,MAAM,iBAAiB,GAAG,UAAU,CAAC,QAA6B,CAAA;YAClE,iBAAiB,CAAC,YAAY,EAAE,CAAA;SACnC;QAED,IAAI,qBAAqB,KAAK,mBAAmB,EAAE;YAC/C,UAAU,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAA;SACrD;IACL,CAAC;IACD,qBAAqB,CAAC,MAAa,EAAE,IAAoB,EAAE,qBAA4C,EACnG,UAA6B,EAAE,OAAgB;QAE/C,UAAU,CAAC,KAAK,EAAE,CAAA;QAClB,IAAI,IAAI,KAAK,MAAM,EAAE;YACjB,UAAU,CAAC,UAAU,EAAE,CAAA;SAC1B;aAAM,IAAI,IAAI,KAAK,WAAW,EAAE;YAC7B,UAAU,CAAC,SAAS,EAAE,CAAA;SACzB;aAAM,IAAI,IAAI,KAAK,aAAa,EAAE;YAC/B,UAAU,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAA;SAC5C;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAA;SAC7C;QAED,IAAI,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC,SAAS,EAAE;YAC5C,MAAM,iBAAiB,GAAG,UAAU,CAAC,QAA6B,CAAA;YAClE,iBAAiB,CAAC,aAAa,EAAE,CAAA;SACpC;QAED,IAAI,qBAAqB,KAAK,oBAAoB,EAAE;YAChD,UAAU,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAA;SACtD;IACL,CAAC;CACJ,CAAA"}