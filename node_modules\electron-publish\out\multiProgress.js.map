{"version": 3, "file": "multiProgress.js", "sourceRoot": "", "sources": ["../src/multiProgress.ts"], "names": [], "mappings": ";;;AAAA,+CAAyC;AACzC,yCAAwC;AAExC,MAAa,aAAa;IAA1B;QACmB,WAAM,GAAG,OAAO,CAAC,MAAa,CAAA;QACvC,WAAM,GAAG,CAAC,CAAA;QAEV,eAAU,GAAG,CAAC,CAAA;QAEd,uBAAkB,GAAG,KAAK,CAAA;QAE1B,aAAQ,GAAG,CAAC,CAAA;IA2EtB,CAAC;IAzEC,SAAS,CAAC,MAAc,EAAE,OAAY;QACpC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAE5B,4DAA4D;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,MAAM,gBAAiB,SAAQ,sBAAW;YAGxC,YAAY,MAAc,EAAE,OAAY;gBACtC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;gBAHhB,UAAK,GAAG,CAAC,CAAC,CAAA;YAIlB,CAAC;YAED,MAAM;gBACJ,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACtB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAA;oBAC/B,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;gBAC1B,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAChC,CAAC;gBAED,KAAK,CAAC,MAAM,EAAE,CAAA;gBAEd,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAChC,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAA;oBACjC,IAAA,yBAAU,EAAC,OAAO,CAAC,EAAE;wBACnB,IAAI,YAAY,GAAG,CAAC,CAAA;wBACpB,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;wBACxC,OAAO,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC;4BACzB,YAAY,EAAE,CAAA;4BACd,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,CAAA;wBACtD,CAAC;wBAED,OAAO,CAAC,aAAa,CAAC,YAAY,GAAG,CAAC,CAAC,CAAA;wBACvC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;oBAC/B,CAAC,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;YAED,SAAS;gBACP,OAAO,CAAC,QAAQ,EAAE,CAAA;gBAClB,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;oBACrD,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;oBACxB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAA;oBACtB,OAAO,CAAC,MAAM,GAAG,CAAC,CAAA;oBAClB,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAA;oBAChB,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAA;gBACpC,CAAC;YACH,CAAC;SACF;QAED,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACjD,IAAI,CAAC,QAAQ,EAAE,CAAA;QACf,OAAO,GAAG,CAAA;IACZ,CAAC;IAEO,aAAa,CAAC,KAAa;QACjC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QAC9C,oHAAoH;QACpH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACvB,IAAI,CAAC,UAAU,IAAI,KAAK,CAAA;QACxB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;IACnC,CAAC;IAEO,UAAU,CAAC,KAAa;QAC9B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;QAC9C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;IACrB,CAAC;IAED,SAAS;QACP,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAChC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAA;QACvB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;IACzB,CAAC;CACF;AAnFD,sCAmFC", "sourcesContent": ["import { setPrinter } from \"builder-util\"\nimport { ProgressBar } from \"./progress\"\n\nexport class MultiProgress {\n  private readonly stream = process.stdout as any\n  private cursor = 0\n\n  private totalLines = 0\n\n  private isLogListenerAdded = false\n\n  private barCount = 0\n\n  createBar(format: string, options: any): ProgressBar {\n    options.stream = this.stream\n\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const manager = this\n    class MultiProgressBar extends ProgressBar {\n      private index = -1\n\n      constructor(format: string, options: any) {\n        super(format, options)\n      }\n\n      render() {\n        if (this.index === -1) {\n          this.index = manager.totalLines\n          manager.allocateLines(1)\n        } else {\n          manager.moveCursor(this.index)\n        }\n\n        super.render()\n\n        if (!manager.isLogListenerAdded) {\n          manager.isLogListenerAdded = true\n          setPrinter(message => {\n            let newLineCount = 0\n            let newLineIndex = message.indexOf(\"\\n\")\n            while (newLineIndex > -1) {\n              newLineCount++\n              newLineIndex = message.indexOf(\"\\n\", ++newLineIndex)\n            }\n\n            manager.allocateLines(newLineCount + 1)\n            manager.stream.write(message)\n          })\n        }\n      }\n\n      terminate() {\n        manager.barCount--\n        if (manager.barCount === 0 && manager.totalLines > 0) {\n          manager.allocateLines(1)\n          manager.totalLines = 0\n          manager.cursor = 0\n          setPrinter(null)\n          manager.isLogListenerAdded = false\n        }\n      }\n    }\n\n    const bar = new MultiProgressBar(format, options)\n    this.barCount++\n    return bar\n  }\n\n  private allocateLines(count: number) {\n    this.stream.moveCursor(0, this.totalLines - 1)\n    // if cursor pointed to previous line where \\n is already printed, another \\n is ignored, so, we can simply print it\n    this.stream.write(\"\\n\")\n    this.totalLines += count\n    this.cursor = this.totalLines - 1\n  }\n\n  private moveCursor(index: number) {\n    this.stream.moveCursor(0, index - this.cursor)\n    this.cursor = index\n  }\n\n  terminate() {\n    this.moveCursor(this.totalLines)\n    this.stream.clearLine()\n    this.stream.cursorTo(0)\n  }\n}\n"]}