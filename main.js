const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

let mainWindow;
let controlWindow;

function createWindows() {
  // Create the banner window
  mainWindow = new BrowserWindow({
    width: 1920,
    height: 200,
    transparent: true,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: false
    }
  });

  // Create the control panel window
  controlWindow = new BrowserWindow({
    width: 800,
    height: 600,
    title: 'Hookah Ads Control Panel',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: false
    }
  });

  mainWindow.loadFile('banner.html');
  controlWindow.loadFile('control.html');

  // Position the banner at the bottom of the screen
  try {
    const { screen } = require('electron');
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    mainWindow.setPosition(0, height - 200);
    mainWindow.setSize(width, 200);
  } catch (error) {
    console.error('Error positioning banner window:', error);
    // Fallback positioning
    mainWindow.setPosition(0, 800);
  }
  // When control window is closed, close the banner window and quit the app
  controlWindow.on('closed', () => {
    if (mainWindow) {
      mainWindow.close();
    }
    app.quit();
  });

  // If banner window is closed, close control window and quit the app
  mainWindow.on('closed', () => {
    if (controlWindow) {
      controlWindow.close();
    }
    app.quit();
  });
}

app.whenReady().then(createWindows);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle settings updates
ipcMain.on('update-settings', (event, settings) => {
  mainWindow.webContents.send('apply-settings', settings);
});

// Template file management
const templatesDir = path.join(__dirname, 'templates');
const templatesFile = path.join(templatesDir, 'saved-templates.json');

// Ensure templates directory exists
function ensureTemplatesDir() {
  if (!fs.existsSync(templatesDir)) {
    fs.mkdirSync(templatesDir, { recursive: true });
    console.log('Created templates directory:', templatesDir);
  }
}

// Save templates to file
ipcMain.on('save-templates', (event, templatesJson) => {
  try {
    ensureTemplatesDir();
    fs.writeFileSync(templatesFile, templatesJson, 'utf8');
    console.log('Templates saved to file:', templatesFile);

    // Send success response
    if (controlWindow) {
      controlWindow.webContents.send('template-saved', 'all', 'All templates');
    }
  } catch (error) {
    console.error('Error saving templates to file:', error);

    // Send error response
    if (controlWindow) {
      controlWindow.webContents.send('template-save-error', 'all', error.message);
    }
  }
});

// Load templates from file
ipcMain.on('load-templates', (event) => {
  try {
    if (fs.existsSync(templatesFile)) {
      const templatesData = fs.readFileSync(templatesFile, 'utf8');
      console.log('Templates loaded from file:', templatesFile);

      // Send templates data
      if (controlWindow) {
        controlWindow.webContents.send('templates-loaded', templatesData);
      }
    } else {
      console.log('No templates file found, will create defaults');

      // Send null to indicate no saved templates
      if (controlWindow) {
        controlWindow.webContents.send('templates-loaded', null);
      }
    }
  } catch (error) {
    console.error('Error loading templates from file:', error);

    // Send error response
    if (controlWindow) {
      controlWindow.webContents.send('templates-load-error', error.message);
    }
  }
});