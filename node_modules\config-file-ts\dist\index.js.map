{"version": 3, "file": "index.js", "sources": ["../src/tsCompile.ts", "../src/compileUtil.ts", "../src/loadTsConfig.ts"], "sourcesContent": ["import * as ts from \"typescript\";\nimport { Program, EmitResult } from \"typescript\";\nimport path from \"path\";\n\nexport interface CompileResult {\n  localSources: string[];\n  compiled: boolean;\n}\n\nexport function tsCompile(\n  fileNames: string[],\n  options: ts.CompilerOptions\n): CompileResult {\n  console.log(\"compiling:\", fileNames);\n  const program = ts.createProgram(fileNames, options);\n  const sources = program\n    .getSourceFiles()\n    .map((f) => f.fileName)\n    .filter((name) => !name.includes(\"node_modules\"));\n  const emitResult = program.emit();\n  logDiagnostics(program, emitResult);\n\n  return { localSources: sources, compiled: !emitResult.emitSkipped };\n}\n\nfunction logDiagnostics(program: Program, emitResult: EmitResult): void {\n  const allDiagnostics = ts\n    .getPreEmitDiagnostics(program)\n    .concat(emitResult.diagnostics);\n\n  allDiagnostics.forEach((diagnostic) => {\n    if (diagnostic.file) {\n      const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(\n        diagnostic.start!\n      );\n      const message = ts.flattenDiagnosticMessageText(\n        diagnostic.messageText,\n        \"\\n\"\n      );\n\n      const filePath = path.resolve(diagnostic.file.fileName);\n      console.log(\n        `tsc: (${filePath}:${line + 1}:${character + 1}): ${message}`\n      );\n    } else {\n      console.log(\n        ts.flattenDiagnosticMessageText(diagnostic.messageText, \"\\n\")\n      );\n    }\n  });\n}\n", "import { glob } from \"glob\";\nimport path from \"path\";\nimport { tsCompile } from \"./tsCompile\";\nimport ts from \"typescript\";\nimport fs from \"fs\";\n\nconst fsRoot = path.parse(process.cwd()).root;\n\n/** Return true if any files need compiling */\nexport function needsCompile(srcGlobs: string[], outDir: string): boolean {\n  const files = srcGlobs.flatMap((src) => glob.sync(src));\n  const srcDestPairs = compilationPairs(files, outDir);\n  return anyOutDated(srcDestPairs);\n}\n\n/** Return true if all files exist on the filesystem */\nexport function expectFilesExist(files: string[]): boolean {\n  const missing = files.find((file) => !fs.existsSync(file));\n  if (missing) {\n    return false;\n  }\n  return true;\n}\n\n/** @return path to the js file that will be produced by typescript compilation */\nexport function jsOutFile(tsFile: string, outDir: string): string {\n  const tsAbsolutePath = path.resolve(tsFile);\n  const tsAbsoluteDir = path.dirname(tsAbsolutePath);\n  const dirFromRoot = path.relative(fsRoot, tsAbsoluteDir);\n  const jsDir = path.join(outDir, dirFromRoot);\n  const outFile = changeSuffix(path.basename(tsFile), \".js\");\n  return path.join(jsDir, outFile);\n}\n\n// for tests\nexport let _compileCount = 0; \nexport function _withCompileCount(fn:() => void):number {\n  _compileCount = 0;\n  fn();\n  return _compileCount;\n}\n\n\n/* \nWe set rootDir to fsRoot for tsc compilation.\n\nThat means that the .js output files produced by typescript will be in a deep tree\nof subdirectories mirroring the path from / to the source file.  \n  e.g. /home/<USER>/proj/foo.ts will output to outdir/home/<USER>/lee/foo.js.\n\nWe need to set a rootDir so that the output tree js files produced by typescript is\npredictable prior to compilation. Without a rootDir, tsc will make an output tree that\nis as short as possible depending on the imports used by the .ts files. Shorter is nice, \nbut the unpredictability breaks checks for on-demand compilation.\n\nA .ts file can import from parent directories.\n  e.g. import * from \"../util\". \nSo we use the file system root as the rootDir to be conservative in handling\npotential parent directory imports.\n*/\n\n\nexport function compileIfNecessary(\n  sources: string[],\n  outDir: string,\n  strict = true\n): boolean {\n  const sourceSet = new Set([...sources, ...extendedSources(outDir)]);\n  const allSources = [...sourceSet];\n  if (needsCompile(allSources, outDir)) {\n    _compileCount++;\n    const { compiled, localSources } = tsCompile(sources, {\n      outDir,\n      rootDir: fsRoot,\n      module: ts.ModuleKind.CommonJS,\n      moduleResolution: ts.ModuleResolutionKind.NodeJs,\n      esModuleInterop: true,\n      resolveJsonModule: true,\n      skipLibCheck: true,\n      strict,\n      target: ts.ScriptTarget.ES2019,\n      noImplicitAny: false,\n      noEmitOnError: true,\n    });\n    if (compiled) {\n      saveExtendedSources(outDir, localSources);\n      linkNodeModules(outDir);\n    }\n    return compiled;\n  }\n  return true;\n}\n\n/** local sources used in last compilation, including imports */\nfunction extendedSources(outDir: string): string[] {\n  const file = sourcesFile(outDir);\n  if (!fs.existsSync(file)) {\n    return [];\n  }\n  const lines = fs.readFileSync(file, \"utf8\");\n  return lines.split(\"\\n\");\n}\n\nfunction sourcesFile(outDir: string): string {\n  return path.join(outDir, \"_sources\");\n}\n\nfunction saveExtendedSources(outDir: string, allSources: string[]): void {\n  const file = sourcesFile(outDir);\n  fs.writeFileSync(file, allSources.join(\"\\n\"));\n}\n\n/** Put a link in the output directory to node_modules.\n */\nfunction linkNodeModules(outDir: string): void {\n  /*\n   * Note that this only puts a link to the single node_modules directory\n   * that's closest by.\n   *\n   * But I think node's module resolution will search multiple\n   * parent directories for multiple node_modules at runtime. So just one\n   * node_modules link may be insufficient in some complicated cases.\n   *\n   * If supporting the more complicated case is worthwhile, we can consider\n   * e.g. encoding a full list of node_modules and setting NODE_PATH instead\n   * of the symlink approach here.\n   */\n  const nodeModules = nearestNodeModules(process.cwd());\n  if (nodeModules) {\n    const linkToModules = path.join(outDir, \"node_modules\");\n    symLinkForce(nodeModules, linkToModules);\n  }\n}\n\n/** create a symlink, replacing any existing linkfile */\nexport function symLinkForce(existing: string, link: string): void {\n  if (fs.existsSync(link)) {\n    if (!fs.lstatSync(link).isSymbolicLink()) {\n      throw `symLinkForce refusing to unlink non-symlink ${link}`;\n    }\n    fs.unlinkSync(link);\n  }\n  fs.symlinkSync(existing, link);\n}\n\n/** @return the resolved path to the nearest node_modules file,\n * either in the provided directory or a parent.\n */\nexport function nearestNodeModules(dir: string): string | undefined {\n  const resolvedDir = path.resolve(dir);\n  const modulesFile = path.join(resolvedDir, \"node_modules\");\n\n  if (fs.existsSync(modulesFile)) {\n    return modulesFile;\n  } else {\n    const { dir: parent, root } = path.parse(resolvedDir);\n    if (parent !== root) {\n      return nearestNodeModules(parent);\n    } else {\n      return undefined;\n    }\n  }\n}\n\n/**\n * Compile a typescript config file to js if necessary (if the js\n * file doesn't exist or is older than the typescript file).\n *\n * @param tsFile path to ts config file\n * @param outDir directory to place the compiled js file\n * @returns the path to the compiled javascript config file,\n *   or undefined if the compilation fails.\n */\nexport function compileConfigIfNecessary(\n  tsFile: string,\n  outDir: string,\n  strict = true\n): string | undefined {\n  if (!fs.existsSync(tsFile)) {\n    console.error(\"config file:\", tsFile, \" not found\");\n    return undefined;\n  }\n\n  const success = compileIfNecessary([tsFile], outDir, strict);\n  if (!success) {\n    return undefined;\n  }\n\n  return jsOutFile(tsFile, outDir);\n}\n\nfunction compilationPairs(\n  srcFiles: string[],\n  outDir: string\n): [string, string][] {\n  return srcFiles.map((tsFile) => {\n    return [tsFile, jsOutFile(tsFile, outDir)];\n  });\n}\n\nfunction anyOutDated(filePairs: [string, string][]): boolean {\n  const found = filePairs.find(([srcPath, outPath]) => {\n    if (!fs.existsSync(outPath)) {\n      return true;\n    }\n    const srcTime = fs.statSync(srcPath).mtime;\n    const outTime = fs.statSync(outPath).mtime;\n    return srcTime > outTime;\n  });\n\n  return found !== undefined;\n}\n\nfunction changeSuffix(filePath: string, suffix: string): string {\n  const dir = path.dirname(filePath);\n  const curSuffix = path.extname(filePath);\n  const base = path.basename(filePath, curSuffix);\n  return path.join(dir, base + suffix);\n}\n", "import { compileConfigIfNecessary } from \"./compileUtil\";\nimport os, { platform } from \"os\";\nimport path from \"path\";\n\n/** Load a typescript configuration file.\n * For speed, the typescript file is transpiled to javascript and cached.\n *\n * @param T type of default export value in the configuration file\n * @param outDir location to store the compiled javascript. \n *  Defaults to $HOME/.cache/config-file-ts/<ts-file-path>/\n * @returns the default exported value from the configuration file or undefined\n */\nexport function loadTsConfig<T>(\n  tsFile: string,\n  outDir?: string | undefined,\n  strict = true\n): T | undefined {\n  const realOutDir = outDir || defaultOutDir(tsFile);\n  const jsConfig = compileConfigIfNecessary(tsFile, realOutDir, strict);\n  if (!jsConfig) {\n    return undefined;\n  }\n\n  const end = jsConfig.length - path.extname(jsConfig).length;\n  const requirePath = jsConfig.slice(0, end);\n  const config = require(requirePath);\n  return config.default;\n}\n\n/** @return the directory that will be used to store transpilation output. */\nexport function defaultOutDir(\n  tsFile: string,\n  programName: string = \"config-file-ts\"\n): string {\n  const tsPath = path.resolve(tsFile);\n  let smushedPath = tsPath\n    .split(path.sep)\n    .join(\"-\")\n    .slice(1);\n  if (platform() === \"win32\") {\n    smushedPath = smushedPath.replace(/^:/, \"\");\n  }\n  return path.join(os.homedir(), \".cache\", programName, smushedPath);\n}\n"], "names": ["ts", "glob", "platform"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AASgB,SAAA,SAAS,CACvB,SAAmB,EACnB,OAA2B,EAAA;AAE3B,IAAA,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACrC,MAAM,OAAO,GAAGA,aAAE,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACrD,MAAM,OAAO,GAAG,OAAO;AACpB,SAAA,cAAc,EAAE;SAChB,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACtB,SAAA,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;AACpD,IAAA,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;AAClC,IAAA,cAAc,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAEpC,IAAA,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;AACtE,CAAC;AAED,SAAS,cAAc,CAAC,OAAgB,EAAE,UAAsB,EAAA;IAC9D,MAAM,cAAc,GAAGA,aAAE;SACtB,qBAAqB,CAAC,OAAO,CAAC;AAC9B,SAAA,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAElC,IAAA,cAAc,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;AACpC,QAAA,IAAI,UAAU,CAAC,IAAI,EAAE;AACnB,YAAA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,6BAA6B,CACvE,UAAU,CAAC,KAAM,CAClB,CAAC;AACF,YAAA,MAAM,OAAO,GAAGA,aAAE,CAAC,4BAA4B,CAC7C,UAAU,CAAC,WAAW,EACtB,IAAI,CACL,CAAC;AAEF,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxD,YAAA,OAAO,CAAC,GAAG,CACT,CAAS,MAAA,EAAA,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAA,CAAA,EAAI,SAAS,GAAG,CAAC,MAAM,OAAO,CAAA,CAAE,CAC9D,CAAC;SACH;aAAM;AACL,YAAA,OAAO,CAAC,GAAG,CACTA,aAAE,CAAC,4BAA4B,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAC9D,CAAC;SACH;AACH,KAAC,CAAC,CAAC;AACL;;AC5CA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC;AAE9C;AACgB,SAAA,YAAY,CAAC,QAAkB,EAAE,MAAc,EAAA;AAC7D,IAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,KAAKC,SAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACxD,MAAM,YAAY,GAAG,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACrD,IAAA,OAAO,WAAW,CAAC,YAAY,CAAC,CAAC;AACnC,CAAC;AAED;AACM,SAAU,gBAAgB,CAAC,KAAe,EAAA;AAC9C,IAAA,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,IAAI,OAAO,EAAE;AACX,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;AACgB,SAAA,SAAS,CAAC,MAAc,EAAE,MAAc,EAAA;IACtD,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IACnD,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IACzD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC7C,IAAA,MAAM,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3D,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACnC,CAAC;AAWD;;;;;;;;;;;;;;;;AAgBE;AAGI,SAAU,kBAAkB,CAChC,OAAiB,EACjB,MAAc,EACd,MAAM,GAAG,IAAI,EAAA;AAEb,IAAA,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpE,IAAA,MAAM,UAAU,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;AAClC,IAAA,IAAI,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;QAEpC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE;YACpD,MAAM;AACN,YAAA,OAAO,EAAE,MAAM;AACf,YAAA,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ;AAC9B,YAAA,gBAAgB,EAAE,EAAE,CAAC,oBAAoB,CAAC,MAAM;AAChD,YAAA,eAAe,EAAE,IAAI;AACrB,YAAA,iBAAiB,EAAE,IAAI;AACvB,YAAA,YAAY,EAAE,IAAI;YAClB,MAAM;AACN,YAAA,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM;AAC9B,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,aAAa,EAAE,IAAI;AACpB,SAAA,CAAC,CAAC;QACH,IAAI,QAAQ,EAAE;AACZ,YAAA,mBAAmB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAC1C,eAAe,CAAC,MAAM,CAAC,CAAC;SACzB;AACD,QAAA,OAAO,QAAQ,CAAC;KACjB;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;AACA,SAAS,eAAe,CAAC,MAAc,EAAA;AACrC,IAAA,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IACjC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACxB,QAAA,OAAO,EAAE,CAAC;KACX;IACD,MAAM,KAAK,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC5C,IAAA,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,WAAW,CAAC,MAAc,EAAA;IACjC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAc,EAAE,UAAoB,EAAA;AAC/D,IAAA,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;AACjC,IAAA,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD,CAAC;AAED;AACG;AACH,SAAS,eAAe,CAAC,MAAc,EAAA;AACrC;;;;;;;;;;;AAWG;IACH,MAAM,WAAW,GAAG,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IACtD,IAAI,WAAW,EAAE;QACf,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AACxD,QAAA,YAAY,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;KAC1C;AACH,CAAC;AAED;AACgB,SAAA,YAAY,CAAC,QAAgB,EAAE,IAAY,EAAA;AACzD,IAAA,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;YACxC,MAAM,CAAA,4CAAA,EAA+C,IAAI,CAAA,CAAE,CAAC;SAC7D;AACD,QAAA,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KACrB;AACD,IAAA,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACjC,CAAC;AAED;;AAEG;AACG,SAAU,kBAAkB,CAAC,GAAW,EAAA;IAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACtC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AAE3D,IAAA,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;AAC9B,QAAA,OAAO,WAAW,CAAC;KACpB;SAAM;AACL,QAAA,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AACtD,QAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,YAAA,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;SACnC;aAAM;AACL,YAAA,OAAO,SAAS,CAAC;SAClB;KACF;AACH,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,wBAAwB,CACtC,MAAc,EACd,MAAc,EACd,MAAM,GAAG,IAAI,EAAA;IAEb,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC1B,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;AACpD,QAAA,OAAO,SAAS,CAAC;KAClB;AAED,IAAA,MAAM,OAAO,GAAG,kBAAkB,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAC7D,IAAI,CAAC,OAAO,EAAE;AACZ,QAAA,OAAO,SAAS,CAAC;KAClB;AAED,IAAA,OAAO,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACnC,CAAC;AAED,SAAS,gBAAgB,CACvB,QAAkB,EAClB,MAAc,EAAA;AAEd,IAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,KAAI;QAC7B,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,WAAW,CAAC,SAA6B,EAAA;AAChD,IAAA,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,KAAI;QAClD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAC3B,YAAA,OAAO,IAAI,CAAC;SACb;QACD,MAAM,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;QAC3C,MAAM,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;QAC3C,OAAO,OAAO,GAAG,OAAO,CAAC;AAC3B,KAAC,CAAC,CAAC;IAEH,OAAO,KAAK,KAAK,SAAS,CAAC;AAC7B,CAAC;AAED,SAAS,YAAY,CAAC,QAAgB,EAAE,MAAc,EAAA;IACpD,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACnC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACzC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IAChD,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,GAAG,MAAM,CAAC,CAAC;AACvC;;ACtNA;;;;;;;AAOG;AACG,SAAU,YAAY,CAC1B,MAAc,EACd,MAA2B,EAC3B,MAAM,GAAG,IAAI,EAAA;IAEb,MAAM,UAAU,GAAG,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;IACnD,MAAM,QAAQ,GAAG,wBAAwB,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IACtE,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,OAAO,SAAS,CAAC;KAClB;AAED,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;IAC5D,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC3C,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;IACpC,OAAO,MAAM,CAAC,OAAO,CAAC;AACxB,CAAC;AAED;SACgB,aAAa,CAC3B,MAAc,EACd,cAAsB,gBAAgB,EAAA;IAEtC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACpC,IAAI,WAAW,GAAG,MAAM;AACrB,SAAA,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;SACf,IAAI,CAAC,GAAG,CAAC;SACT,KAAK,CAAC,CAAC,CAAC,CAAC;AACZ,IAAA,IAAIC,WAAQ,EAAE,KAAK,OAAO,EAAE;QAC1B,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KAC7C;AACD,IAAA,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;AACrE;;;;;;;;;"}