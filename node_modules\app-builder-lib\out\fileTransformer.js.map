{"version": 3, "file": "fileTransformer.js", "sourceRoot": "", "sources": ["../src/fileTransformer.ts"], "names": [], "mappings": ";;;AAUA,sDAOC;AAGD,wBAGC;AAGD,8CA0BC;AAUD,gEAGC;AAjED,+CAAsE;AACtE,0CAAsC;AACtC,6BAA4B;AAI5B,gBAAgB;AACH,QAAA,oBAAoB,GAAG,GAAG,IAAI,CAAC,GAAG,eAAe,IAAI,CAAC,GAAG,EAAE,CAAA;AAExE,gBAAgB;AAChB,SAAgB,qBAAqB,CAAC,IAAc;IAClD,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAA;IACpC,CAAC;IAED,oJAAoJ;IACpJ,OAAO,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;AACzC,CAAC;AAED,gBAAgB;AAChB,SAAgB,MAAM,CAAC,IAAY,EAAE,IAAc;IACjD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAA;IACvC,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAA;AACrC,CAAC;AAED,gBAAgB;AAChB,SAAgB,iBAAiB,CAAC,MAAc,EAAE,aAA4B,EAAE,aAAkB,EAAE,gBAAwC;IAC1I,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;IACzD,MAAM,sBAAsB,GAAG,aAAa,CAAC,oBAAoB,KAAK,KAAK,CAAA;IAC3E,MAAM,uBAAuB,GAAG,aAAa,CAAC,qBAAqB,KAAK,KAAK,CAAA;IAC7E,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,GAAG,cAAc,CAAA;IAC7C,OAAO,IAAI,CAAC,EAAE;QACZ,IAAI,IAAI,KAAK,eAAe,EAAE,CAAC;YAC7B,OAAO,qBAAqB,CAAC,IAAI,EAAE,aAAa,EAAE,sBAAsB,EAAE,uBAAuB,CAAC,CAAA;QACpG,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,4BAAoB,CAAC,EAAE,CAAC;YACtE,OAAO,IAAA,mBAAQ,EAAC,IAAI,EAAE,OAAO,CAAC;iBAC3B,IAAI,CAAC,EAAE,CAAC,EAAE,CACT,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;gBACjC,MAAM,EAAE,KAAK;gBACb,sBAAsB;gBACtB,uBAAuB;aACxB,CAAC,CACH;iBACA,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,kBAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QACnC,CAAC;aAAM,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;YACpC,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAA;QAC/B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC;AASD,gBAAgB;AAChB,SAAgB,0BAA0B,CAAC,UAAkB,EAAE,QAAgB;IAC7E,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAA;IAC5F,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC,CAAC,iCAAiC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;AACzH,CAAC;AAED,MAAM,gCAAgC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,cAAc,EAAE,oBAAoB,EAAE,MAAM,CAAC,CAAC,CAAA;AAQxK,SAAS,kBAAkB,CAAC,IAAS,EAAE,OAAkC;IACvE,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAA;IAC9B,0FAA0F;IAC1F,MAAM,aAAa,GAAG,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAA;IACtI,IAAI,CAAC;QACH,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YACpD,mHAAmH;YACnH,IACE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;gBACf,gCAAgC,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC1C,CAAC,OAAO,CAAC,sBAAsB,IAAI,IAAI,KAAK,SAAS,CAAC;gBACtD,CAAC,OAAO,CAAC,uBAAuB,IAAI,IAAI,KAAK,UAAU,CAAC;gBACxD,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,iBAAiB,CAAC;gBAC9C,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC;gBACpC,CAAC,aAAa,IAAI,IAAI,KAAK,OAAO,CAAC,EACnC,CAAC;gBACD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAA;gBACjB,OAAO,GAAG,IAAI,CAAA;YAChB,CAAC;QACH,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;QACtC,CAAC;IACH,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,IAAA,oBAAK,EAAC,CAAC,CAAC,CAAA;IACV,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,IAAY,EAAE,aAAkB,EAAE,sBAA+B,EAAE,uBAAgC;IACtI,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA;IACjE,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,IAAA,yBAAU,EAAC,eAAe,EAAE,aAAa,CAAC,CAAA;IAC5C,CAAC;IAED,oEAAoE;IACpE,MAAM,uBAAuB,GAAG,kBAAkB,CAAC,eAAe,EAAE;QAClE,MAAM,EAAE,IAAI;QACZ,sBAAsB;QACtB,uBAAuB;KACxB,CAAC,CAAA;IACF,IAAI,uBAAuB,IAAI,IAAI,EAAE,CAAC;QACpC,OAAO,uBAAuB,CAAA;IAChC,CAAC;SAAM,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;IACjD,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC", "sourcesContent": ["import { debug, deepAssign, FileTransformer, log } from \"builder-util\"\nimport { readFile } from \"fs/promises\"\nimport * as path from \"path\"\nimport { Configuration } from \"./configuration\"\nimport { Packager } from \"./packager\"\n\n/** @internal */\nexport const NODE_MODULES_PATTERN = `${path.sep}node_modules${path.sep}`\n\n/** @internal */\nexport function isElectronCompileUsed(info: Packager): boolean {\n  if (info.config.electronCompile != null) {\n    return info.config.electronCompile\n  }\n\n  // if in devDependencies - it means that babel is used for precompilation or for some reason user decided to not use electron-compile for production\n  return hasDep(\"electron-compile\", info)\n}\n\n/** @internal */\nexport function hasDep(name: string, info: Packager) {\n  const deps = info.metadata.dependencies\n  return deps != null && name in deps\n}\n\n/** @internal */\nexport function createTransformer(srcDir: string, configuration: Configuration, extraMetadata: any, extraTransformer: FileTransformer | null): FileTransformer {\n  const mainPackageJson = path.join(srcDir, \"package.json\")\n  const isRemovePackageScripts = configuration.removePackageScripts !== false\n  const isRemovePackageKeywords = configuration.removePackageKeywords !== false\n  const packageJson = path.sep + \"package.json\"\n  return file => {\n    if (file === mainPackageJson) {\n      return modifyMainPackageJson(file, extraMetadata, isRemovePackageScripts, isRemovePackageKeywords)\n    }\n\n    if (file.endsWith(packageJson) && file.includes(NODE_MODULES_PATTERN)) {\n      return readFile(file, \"utf-8\")\n        .then(it =>\n          cleanupPackageJson(JSON.parse(it), {\n            isMain: false,\n            isRemovePackageScripts,\n            isRemovePackageKeywords,\n          })\n        )\n        .catch((e: any) => log.warn(e))\n    } else if (extraTransformer != null) {\n      return extraTransformer(file)\n    } else {\n      return null\n    }\n  }\n}\n\n/** @internal */\nexport interface CompilerHost {\n  compile(file: string): any\n\n  saveConfiguration(): Promise<any>\n}\n\n/** @internal */\nexport function createElectronCompilerHost(projectDir: string, cacheDir: string): Promise<CompilerHost> {\n  const electronCompilePath = path.join(projectDir, \"node_modules\", \"electron-compile\", \"lib\")\n  return require(path.join(electronCompilePath, \"config-parser\")).createCompilerHostFromProjectRoot(projectDir, cacheDir)\n}\n\nconst ignoredPackageMetadataProperties = new Set([\"dist\", \"gitHead\", \"build\", \"jspm\", \"ava\", \"xo\", \"nyc\", \"eslintConfig\", \"contributors\", \"bundleDependencies\", \"tags\"])\n\ninterface CleanupPackageFileOptions {\n  readonly isRemovePackageScripts: boolean\n  readonly isRemovePackageKeywords: boolean\n  readonly isMain: boolean\n}\n\nfunction cleanupPackageJson(data: any, options: CleanupPackageFileOptions): any {\n  const deps = data.dependencies\n  // https://github.com/electron-userland/electron-builder/issues/507#issuecomment-312772099\n  const isRemoveBabel = deps != null && typeof deps === \"object\" && !Object.getOwnPropertyNames(deps).some(it => it.startsWith(\"babel\"))\n  try {\n    let changed = false\n    for (const prop of Object.getOwnPropertyNames(data)) {\n      // removing devDependencies from package.json breaks levelup in electron, so, remove it only from main package.json\n      if (\n        prop[0] === \"_\" ||\n        ignoredPackageMetadataProperties.has(prop) ||\n        (options.isRemovePackageScripts && prop === \"scripts\") ||\n        (options.isRemovePackageKeywords && prop === \"keywords\") ||\n        (options.isMain && prop === \"devDependencies\") ||\n        (!options.isMain && prop === \"bugs\") ||\n        (isRemoveBabel && prop === \"babel\")\n      ) {\n        delete data[prop]\n        changed = true\n      }\n    }\n\n    if (changed) {\n      return JSON.stringify(data, null, 2)\n    }\n  } catch (e: any) {\n    debug(e)\n  }\n\n  return null\n}\n\nasync function modifyMainPackageJson(file: string, extraMetadata: any, isRemovePackageScripts: boolean, isRemovePackageKeywords: boolean) {\n  const mainPackageData = JSON.parse(await readFile(file, \"utf-8\"))\n  if (extraMetadata != null) {\n    deepAssign(mainPackageData, extraMetadata)\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/1212\n  const serializedDataIfChanged = cleanupPackageJson(mainPackageData, {\n    isMain: true,\n    isRemovePackageScripts,\n    isRemovePackageKeywords,\n  })\n  if (serializedDataIfChanged != null) {\n    return serializedDataIfChanged\n  } else if (extraMetadata != null) {\n    return JSON.stringify(mainPackageData, null, 2)\n  }\n  return null\n}\n"]}