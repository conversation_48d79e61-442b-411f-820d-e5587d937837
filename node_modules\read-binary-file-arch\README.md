# read-binary-file-arch

A node module that reads the [CPU architecture](https://nodejs.org/api/process.html#processarch) of a binary file.

	npm install read-binary-file-arch

## Usage

Pass a file path to `readBinaryFileArch`.

> [!NOTE]  
> Windows only supports valid PE binary files.

```js
var { readBinaryFileArch } = require('read-binary-file-arch');

readBinaryFileArch(filePath).then((arch) => {
  console.log('arch: ', arch);
}).catch((error) => {
  console.error(error.message);
});
```

## License

MIT
