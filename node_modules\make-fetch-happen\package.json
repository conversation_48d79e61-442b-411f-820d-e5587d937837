{"name": "make-fetch-happen", "version": "10.2.1", "description": "Opinionated, caching, retrying fetch client", "main": "lib/index.js", "files": ["bin/", "lib/"], "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/make-fetch-happen.git"}, "keywords": ["http", "request", "fetch", "mean girls", "caching", "cache", "subresource integrity"], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"agentkeepalive": "^4.2.1", "cacache": "^16.1.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "minipass": "^3.1.6", "minipass-collect": "^1.0.2", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "socks-proxy-agent": "^7.0.0", "ssri": "^9.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "mkdirp": "^1.0.4", "nock": "^13.2.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "tap": "^16.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "tap": {"color": 1, "files": "test/*.js", "check-coverage": true, "timeout": 60}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}}