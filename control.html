<!DOCTYPE html>
<html>
<head>
  <title>Banner Control Panel</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    .control-group {
      margin-bottom: 20px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input, button {
      margin: 5px 0;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    button {
      background-color: #007bff;
      color: white;
      cursor: pointer;
      font-size: 16px;
      padding: 10px 20px;
    }
    button:hover {
      background-color: #0056b3;
    }
    input[type="range"] {
      width: 200px;
    }
  </style>
</head>
<body>
  <div class="control-group">
    <label>Banner Text:</label>
    <input type="text" id="text" style="width: 100%">
  </div>

  <div class="control-group">
    <label>Banner Height (2cm - 8cm):</label>
    <input type="range" id="height" min="75" max="300" value="75">
    <span id="heightValue">2cm</span>
  </div>

  <div class="control-group">
    <label>Background Color:</label>
    <input type="color" id="backgroundColor" value="#000000">
  </div>

  <div class="control-group">
    <label>Text Color:</label>
    <input type="color" id="textColor" value="#ffffff">
  </div>

  <div class="control-group">
    <label>Font Size:</label>
    <select id="fontSize" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="50">Small (50% of banner height)</option>
      <option value="60">Medium-Small (60% of banner height)</option>
      <option value="70" selected>Medium (70% of banner height)</option>
      <option value="80">Medium-Large (80% of banner height)</option>
      <option value="90">Large (90% of banner height)</option>
    </select>
  </div>

  <div class="control-group">
    <label>Scroll Speed (seconds per cycle):</label>
    <input type="range" id="scrollSpeed" min="10" max="60" value="20">
    <span id="speedValue">20s</span>
  </div>

  <div class="control-group">
    <label>Add Images:</label>
    <input type="file" id="imageInput" accept="image/*" multiple>
    <div id="imageStatus" style="margin-top: 10px; font-size: 14px; color: #666;"></div>
  </div>

  <button onclick="applySettings()">Apply Changes</button>

  <script>
    const { ipcRenderer } = require('electron');
    let selectedImages = [];

    // Height slider
    const heightSlider = document.getElementById('height');
    const heightValue = document.getElementById('heightValue');
    heightSlider.oninput = () => {
      const cm = (heightSlider.value / 37.5).toFixed(1);
      heightValue.textContent = cm + 'cm';
    };

    // Speed slider
    const speedSlider = document.getElementById('scrollSpeed');
    const speedValue = document.getElementById('speedValue');
    speedSlider.oninput = () => {
      speedValue.textContent = speedSlider.value + 's';
    };

    // Image handling
    document.getElementById('imageInput').onchange = (e) => {
      const files = Array.from(e.target.files);
      selectedImages = files.map(file => file.path);

      // Update status display
      const statusDiv = document.getElementById('imageStatus');
      if (files.length > 0) {
        const fileNames = files.map(file => file.name).join(', ');
        statusDiv.innerHTML = `<strong>Selected ${files.length} image(s):</strong><br>${fileNames}`;
        statusDiv.style.color = '#4CAF50';
      } else {
        statusDiv.innerHTML = 'No images selected';
        statusDiv.style.color = '#666';
      }

      console.log('Selected image paths:', selectedImages);
    };

    function applySettings() {
      try {
        const settings = {
          text: document.getElementById('text').value || '',
          height: parseInt(document.getElementById('height').value) || 75,
          backgroundColor: document.getElementById('backgroundColor').value || '#000000',
          textColor: document.getElementById('textColor').value || '#ffffff',
          fontSize: parseInt(document.getElementById('fontSize').value) || 70,
          scrollSpeed: parseInt(document.getElementById('scrollSpeed').value) || 20,
          images: selectedImages || []
        };

        ipcRenderer.send('update-settings', settings);

        // Visual feedback
        const button = document.querySelector('button');
        const originalText = button.textContent;
        button.textContent = 'Applied!';
        button.style.backgroundColor = '#4CAF50';
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = '';
        }, 1000);
      } catch (error) {
        console.error('Error applying settings:', error);
        alert('Error applying settings. Please try again.');
      }
    }
  </script>
</body>
</html>