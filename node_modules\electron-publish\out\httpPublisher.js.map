{"version": 3, "file": "httpPublisher.js", "sourceRoot": "", "sources": ["../src/httpPublisher.ts"], "names": [], "mappings": ";;;AAAA,+CAAmC;AACnC,uCAA+B;AAE/B,+BAA+B;AAE/B,2CAAuC;AAEvC,MAAsB,aAAc,SAAQ,qBAAS;IACnD,YACqB,OAAuB,EACzB,sBAAsB,KAAK;QAE5C,KAAK,CAAC,OAAO,CAAC,CAAA;QAHK,YAAO,GAAP,OAAO,CAAgB;QACzB,wBAAmB,GAAnB,mBAAmB,CAAQ;IAG9C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAAgB;QAC3B,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAA,eAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEjG,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,QAAQ,CACjB,QAAQ,EACR,IAAI,CAAC,IAAI,IAAI,mBAAI,CAAC,GAAG,EACrB,IAAI,CAAC,WAAW,CAAC,MAAM,EACvB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAClB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjB,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;wBACpC,OAAO,CAAC,OAAO,EAAE,CAAA;wBACjB,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAA;oBACxC,CAAC,CAAC,CAAA;gBACJ,CAAC;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACtC,CAAC,EACD,IAAI,CAAC,IAAI,CACV,CAAA;YACD,OAAM;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAI,EAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEtC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;QACnE,OAAO,IAAI,CAAC,QAAQ,CAClB,QAAQ,EACR,IAAI,CAAC,IAAI,IAAI,mBAAI,CAAC,GAAG,EACrB,QAAQ,CAAC,IAAI,EACb,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAClB,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gBACxB,gEAAgE;gBAChE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YACvB,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;oBACpC,OAAO,CAAC,OAAO,EAAE,CAAA;oBACjB,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAA;gBACxC,CAAC,CAAC,CAAA;YACJ,CAAC;YACD,OAAO,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACpG,CAAC,EACD,IAAI,CAAC,IAAI,CACV,CAAA;IACH,CAAC;CASF;AA7DD,sCA6DC", "sourcesContent": ["import { Arch } from \"builder-util\"\nimport { stat } from \"fs-extra\"\nimport { ClientRequest } from \"http\"\nimport { basename } from \"path\"\nimport { PublishContext, UploadTask } from \".\"\nimport { Publisher } from \"./publisher\"\n\nexport abstract class HttpPublisher extends Publisher {\n  protected constructor(\n    protected readonly context: PublishContext,\n    private readonly useSafeArtifactName = false\n  ) {\n    super(context)\n  }\n\n  async upload(task: UploadTask): Promise<any> {\n    const fileName = (this.useSafeArtifactName ? task.safeArtifactName : null) || basename(task.file)\n\n    if (task.fileContent != null) {\n      await this.doUpload(\n        fileName,\n        task.arch || Arch.x64,\n        task.fileContent.length,\n        (request, reject) => {\n          if (task.timeout) {\n            request.setTimeout(task.timeout, () => {\n              request.destroy()\n              reject(new Error(\"Request timed out\"))\n            })\n          }\n          return request.end(task.fileContent)\n        },\n        task.file\n      )\n      return\n    }\n\n    const fileStat = await stat(task.file)\n\n    const progressBar = this.createProgressBar(fileName, fileStat.size)\n    return this.doUpload(\n      fileName,\n      task.arch || Arch.x64,\n      fileStat.size,\n      (request, reject) => {\n        if (progressBar != null) {\n          // reset (because can be called several times (several attempts)\n          progressBar.update(0)\n        }\n        if (task.timeout) {\n          request.setTimeout(task.timeout, () => {\n            request.destroy()\n            reject(new Error(\"Request timed out\"))\n          })\n        }\n        return this.createReadStreamAndProgressBar(task.file, fileStat, progressBar, reject).pipe(request)\n      },\n      task.file\n    )\n  }\n\n  protected abstract doUpload(\n    fileName: string,\n    arch: Arch,\n    dataLength: number,\n    requestProcessor: (request: ClientRequest, reject: (error: Error) => void) => void,\n    file: string\n  ): Promise<any>\n}\n"]}