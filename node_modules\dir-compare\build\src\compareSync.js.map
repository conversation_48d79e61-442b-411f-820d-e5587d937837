{"version": 3, "file": "compareSync.js", "sourceRoot": "", "sources": ["../../src/compareSync.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAmB;AACnB,gDAA4B;AAG5B,yDAAqD;AACrD,uDAAmD;AACnD,yDAAmE;AACnE,6DAAyD;AACzD,iDAA4D;AAC5D,wDAAoD;AACpD,oEAAgE;AAEhE;;GAEG;AACH,SAAS,UAAU,CAAC,SAAwB,EAAE,YAAoB,EAAE,YAAqB,EACrF,MAAmB,EAAE,OAAmB;IAExC,IAAI,CAAC,SAAS,IAAI,YAAY,EAAE;QAC5B,OAAO,EAAE,CAAA;KACZ;IACD,IAAI,SAAS,CAAC,WAAW,EAAE;QACvB,IAAI,SAAS,CAAC,kBAAkB,EAAE;YAC9B,OAAO,EAAE,CAAA;SACZ;QACD,MAAM,OAAO,GAAG,YAAE,CAAC,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;QACtD,OAAO,2BAAY,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;KACzF;IACD,OAAO,CAAC,SAAS,CAAC,CAAA;AACtB,CAAC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,UAAyB,EAAE,UAAyB,EAAE,KAAa,EAAE,YAAoB,EACjH,OAAmB,EAAE,UAA6B,EAAE,OAAwB,EAAE,YAA0B;IAExG,MAAM,aAAa,GAAG,2BAAY,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,CAAC,CAAA;IAC5E,MAAM,aAAa,GAAG,2BAAY,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,CAAC,CAAA;IAC5E,2BAAY,CAAC,kBAAkB,CAAC,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,CAAC,CAAA;IAEnG,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;IACrF,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;IACtF,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;IAClB,OAAO,EAAE,GAAG,QAAQ,CAAC,MAAM,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE;QACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC3B,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC3B,IAAI,KAAK,EAAE,KAAK,CAAA;QAEhB,gCAAgC;QAChC,IAAI,GAAG,CAAA;QACP,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE;YAC9C,GAAG,GAAG,iCAAe,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;YAC3D,KAAK,GAAG,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YACjC,KAAK,GAAG,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;SACpC;aAAM,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE;YAC7B,KAAK,GAAG,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YACjC,KAAK,GAAG,qBAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;YACpC,GAAG,GAAG,CAAC,CAAC,CAAA;SACX;aAAM;YACH,KAAK,GAAG,qBAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;YACpC,KAAK,GAAG,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YACjC,GAAG,GAAG,CAAC,CAAA;SACV;QAED,gBAAgB;QAChB,IAAI,GAAG,KAAK,CAAC,EAAE;YACX,wDAAwD;YACxD,IAAI,IAAI,EAAE,MAAM,EAAE,KAAK,CAAA;YACvB,MAAM,qBAAqB,GAAG,uBAAU,CAAC,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAEjF,IAAI,qBAAqB,KAAK,WAAW,EAAE;gBACvC,MAAM,eAAe,GAAG,6BAAa,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;gBACtF,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAA;gBACnD,IAAI,GAAG,eAAe,CAAC,IAAI,CAAA;gBAC3B,MAAM,GAAG,eAAe,CAAC,MAAM,CAAA;aAClC;iBAAM;gBACH,KAAK,GAAG,UAAU,CAAA;gBAClB,IAAI,GAAG,KAAK,CAAA;gBACZ,MAAM,GAAG,mBAAmB,CAAA;aAC/B;YAGD,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAA;YAC9H,mCAAgB,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,qBAAqB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;YACtH,EAAE,EAAE,CAAA;YACJ,EAAE,EAAE,CAAA;YACJ,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,KAAK,KAAK,WAAW,EAAE;gBAC/C,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,cAAS,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,2BAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;aAChK;SACJ;aAAM,IAAI,GAAG,GAAG,CAAC,EAAE;YAChB,gBAAgB;YAChB,MAAM,qBAAqB,GAAG,uBAAU,CAAC,wCAAwC,CAAC,MAAM,CAAC,CAAA;YACzF,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAA;YACrI,mCAAgB,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,qBAAqB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;YAChG,EAAE,EAAE,CAAA;YACJ,IAAI,KAAK,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC/C,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,GAAG,CAAC,EAAE,cAAS,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,2BAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;aACnK;SACJ;aAAM;YACH,eAAe;YACf,MAAM,qBAAqB,GAAG,uBAAU,CAAC,uCAAuC,CAAC,MAAM,CAAC,CAAA;YACxF,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAA;YACtI,mCAAgB,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,qBAAqB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;YACjG,EAAE,EAAE,CAAA;YACJ,IAAI,KAAK,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC/C,WAAW,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,cAAS,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,2BAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;aACnK;SACJ;KACJ;AACL,CAAC;AA5ED,kCA4EC"}